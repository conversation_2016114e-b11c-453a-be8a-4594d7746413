# Import required libraries
import pandas as pd
import numpy as np
from google_play_scraper import app, reviews, Sort
import time
import json
from datetime import datetime
import os
from typing import List, Dict, Tuple
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

print("✅ Libraries imported successfully!")
print(f"📅 Current time: {datetime.now()}")

# Configuration
app_ids = [
    'com.whatsapp',
    'com.facebook.katana',
    'com.instagram.android',
    'com.snapchat.android',
    'com.spotify.music'
]

COUNTRY = 'id'  # Indonesia
LANG = 'jv'    # Indonesian
REVIEWS_PER_APP = 1000  # Number of reviews to scrape per app

print(f"🎯 Target: {len(app_ids)} apps")
print(f"📊 Reviews per app: {REVIEWS_PER_APP}")
print(f"🌍 Country: {COUNTRY}, Language: {LANG}")
print(f"📱 Apps: {app_ids}")

def get_app_info(app_id: str, country: str = 'id', lang: str = 'id') -> Dict:
    """
    Get detailed app information from Google Play Store.
    """
    try:
        app_info = app(app_id, lang=lang, country=country)
        
        return {
            'app_id': app_id,
            'title': app_info.get('title', ''),
            'developer': app_info.get('developer', ''),
            'developer_id': app_info.get('developerId', ''),
            'category': app_info.get('genre', ''),
            'rating': app_info.get('score', 0),
            'rating_count': app_info.get('ratings', 0),
            'installs': app_info.get('installs', ''),
            'price': app_info.get('price', 0),
            'free': app_info.get('free', True),
            'size': app_info.get('size', ''),
            'min_android': app_info.get('minInstalls', ''),
            'content_rating': app_info.get('contentRating', ''),
            'description': app_info.get('description', ''),
            'summary': app_info.get('summary', ''),
            'updated': app_info.get('updated', ''),
            'version': app_info.get('version', ''),
            'recent_changes': app_info.get('recentChanges', ''),
            'scraped_at': datetime.now().isoformat()
        }
        
    except Exception as e:
        print(f"❌ Error getting app info for {app_id}: {e}")
        return {
            'app_id': app_id,
            'error': str(e),
            'scraped_at': datetime.now().isoformat()
        }

def scrape_app_reviews(app_id: str, count: int = 1000, country: str = 'id', lang: str = 'id') -> List[Dict]:
    """
    Scrape reviews for a specific app.
    """
    print(f"\n📱 Scraping {count} reviews for {app_id}...")
    
    try:
        all_reviews = []
        continuation_token = None
        batch_size = 200
        
        with tqdm(total=count, desc=f"Reviews for {app_id}") as pbar:
            while len(all_reviews) < count:
                try:
                    result, continuation_token = reviews(
                        app_id,
                        lang=lang,
                        country=country,
                        sort=Sort.NEWEST,
                        count=min(batch_size, count - len(all_reviews)),
                        continuation_token=continuation_token
                    )
                    
                    if not result:
                        print(f"⚠️ No more reviews available for {app_id}")
                        break
                    
                    # Add metadata to each review
                    for review in result:
                        review['app_id'] = app_id
                        review['scraped_at'] = datetime.now().isoformat()
                    
                    all_reviews.extend(result)
                    pbar.update(len(result))
                    
                    time.sleep(1)  # Rate limiting
                    
                except Exception as e:
                    print(f"❌ Error scraping batch for {app_id}: {e}")
                    break
        
        print(f"✅ Successfully scraped {len(all_reviews)} reviews for {app_id}")
        return all_reviews
        
    except Exception as e:
        print(f"❌ Error scraping reviews for {app_id}: {e}")
        return []

print("✅ Helper functions defined!")

# Scrape app information
print("🔍 Scraping app information...")
apps_info = []

for app_id in tqdm(app_ids, desc="Getting app info"):
    info = get_app_info(app_id, COUNTRY, LANG)
    apps_info.append(info)
    
    if 'error' not in info:
        print(f"✅ {info.get('title', app_id)} - Rating: {info.get('rating', 'N/A')}⭐")
    else:
        print(f"❌ Failed to get info for {app_id}")
    
    time.sleep(1)  # Rate limiting

# Convert to DataFrame
apps_df = pd.DataFrame(apps_info)
print(f"\n📊 App information collected for {len(apps_df)} apps")

# Display results
if not apps_df.empty:
    display(apps_df[['app_id', 'title', 'developer', 'rating', 'rating_count', 'installs']].head())
else:
    print("❌ No app information collected")

# Scrape reviews for all apps
print("💬 Starting review scraping process...")
all_reviews = []

for app_id in app_ids:
    app_reviews = scrape_app_reviews(app_id, REVIEWS_PER_APP, COUNTRY, LANG)
    all_reviews.extend(app_reviews)
    print(f"📈 Total reviews collected so far: {len(all_reviews)}")
    
    # Longer pause between apps to avoid rate limiting
    time.sleep(3)

print(f"\n🎉 Completed! Total reviews scraped: {len(all_reviews)}")

# Process reviews data
if all_reviews:
    reviews_df = pd.DataFrame(all_reviews)
    
    # Add additional features
    reviews_df['review_length'] = reviews_df['content'].str.len()
    reviews_df['word_count'] = reviews_df['content'].str.split().str.len()
    
    # Convert date columns
    reviews_df['at'] = pd.to_datetime(reviews_df['at'])
    
    print(f"📊 Reviews DataFrame created with {len(reviews_df)} rows")
    print(f"📋 Columns: {list(reviews_df.columns)}")
    
    # Show summary by app
    print("\n📱 Reviews by app:")
    app_review_counts = reviews_df['app_id'].value_counts()
    for app_id, count in app_review_counts.items():
        app_name = apps_df[apps_df['app_id'] == app_id]['title'].iloc[0] if not apps_df.empty else app_id
        print(f"  • {app_name}: {count} reviews")
    
    # Display sample
    display(reviews_df[['app_id', 'userName', 'score', 'content', 'at']].head())
else:
    print("❌ No reviews were scraped!")
    reviews_df = pd.DataFrame()

# Create summary statistics
if not reviews_df.empty and not apps_df.empty:
    print("=" * 60)
    print("📊 GOOGLE PLAY STORE SCRAPING SUMMARY")
    print("=" * 60)
    
    print(f"\n🎯 Apps scraped: {len(apps_df)}")
    print(f"💬 Total reviews: {len(reviews_df)}")
    print(f"📅 Date range: {reviews_df['at'].min()} to {reviews_df['at'].max()}")
    
    print("\n📱 App Information Summary:")
    for _, app in apps_df.iterrows():
        if 'title' in app and 'rating' in app and 'error' not in app:
            rating_count = app.get('rating_count', 'N/A')
            installs = app.get('installs', 'N/A')
            print(f"  • {app['title']}: {app['rating']:.1f}⭐ ({rating_count} ratings, {installs} installs)")
    
    print("\n📊 Review Statistics by App:")
    review_stats = reviews_df.groupby('app_id').agg({
        'score': ['count', 'mean'],
        'review_length': 'mean',
        'word_count': 'mean'
    }).round(2)
    
    review_stats.columns = ['Review Count', 'Avg Rating', 'Avg Length', 'Avg Words']
    display(review_stats)
    
    print("\n⭐ Overall Rating Distribution:")
    rating_dist = reviews_df['score'].value_counts().sort_index()
    for rating, count in rating_dist.items():
        percentage = (count / len(reviews_df)) * 100
        stars = "⭐" * rating
        print(f"  {stars} ({rating}): {count} reviews ({percentage:.1f}%)")
else:
    print("❌ No data available for summary")

# Create timestamp for file naming
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
print(f"📅 Export timestamp: {timestamp}")

# Export app information
if not apps_df.empty:
    # CSV export
    apps_csv_filename = f"google_play_apps_info_{timestamp}.csv"
    apps_df.to_csv(apps_csv_filename, index=False, encoding='utf-8')
    print(f"✅ App information saved to: {apps_csv_filename}")
    
    # XLSX export
    try:
        apps_xlsx_filename = f"google_play_apps_info_{timestamp}.xlsx"
        apps_df.to_excel(apps_xlsx_filename, index=False, engine='openpyxl')
        print(f"✅ App information saved to: {apps_xlsx_filename}")
    except ImportError:
        print("⚠️ openpyxl not installed. Install with: pip install openpyxl")
else:
    print("❌ No app information to export")

# Export reviews
if not reviews_df.empty:
    # CSV export
    reviews_csv_filename = f"google_play_reviews_{timestamp}.csv"
    reviews_df.to_csv(reviews_csv_filename, index=False, encoding='utf-8')
    print(f"✅ Reviews saved to: {reviews_csv_filename}")
    
    # XLSX export
    try:
        reviews_xlsx_filename = f"google_play_reviews_{timestamp}.xlsx"
        reviews_df.to_excel(reviews_xlsx_filename, index=False, engine='openpyxl')
        print(f"✅ Reviews saved to: {reviews_xlsx_filename}")
    except ImportError:
        print("⚠️ openpyxl not installed. Install with: pip install openpyxl")
else:
    print("❌ No reviews to export")

print(f"\n📁 All data exported with timestamp: {timestamp}")

# Create a combined Excel file with multiple sheets
if not apps_df.empty or not reviews_df.empty:
    try:
        combined_filename = f"google_play_complete_data_{timestamp}.xlsx"
        
        with pd.ExcelWriter(combined_filename, engine='openpyxl') as writer:
            if not apps_df.empty:
                apps_df.to_excel(writer, sheet_name='App_Information', index=False)
                print(f"✅ App information added to sheet 'App_Information'")
            
            if not reviews_df.empty:
                reviews_df.to_excel(writer, sheet_name='Reviews', index=False)
                print(f"✅ Reviews added to sheet 'Reviews'")
                
                # Create summary sheet
                summary_data = []
                
                # Overall statistics
                summary_data.append(['Metric', 'Value'])
                summary_data.append(['Total Apps', len(apps_df) if not apps_df.empty else 0])
                summary_data.append(['Total Reviews', len(reviews_df)])
                summary_data.append(['Date Range Start', reviews_df['at'].min().strftime('%Y-%m-%d')])
                summary_data.append(['Date Range End', reviews_df['at'].max().strftime('%Y-%m-%d')])
                summary_data.append(['Average Rating', round(reviews_df['score'].mean(), 2)])
                summary_data.append(['', ''])  # Empty row
                
                # Rating distribution
                summary_data.append(['Rating Distribution', ''])
                rating_dist = reviews_df['score'].value_counts().sort_index()
                for rating, count in rating_dist.items():
                    percentage = (count / len(reviews_df)) * 100
                    summary_data.append([f'{rating} Stars', f'{count} ({percentage:.1f}%)'])
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Summary', index=False, header=False)
                print(f"✅ Summary added to sheet 'Summary'")
        
        print(f"\n🎉 Combined Excel file created: {combined_filename}")
        print(f"   📊 Contains: App Information, Reviews, and Summary sheets")
        
    except ImportError:
        print("⚠️ openpyxl not installed. Combined Excel file not created.")
        print("   Install with: pip install openpyxl")
    except Exception as e:
        print(f"❌ Error creating combined Excel file: {e}")
else:
    print("❌ No data available to create combined file")

# Display final summary and file information
print("=" * 70)
print("🎉 SCRAPING COMPLETED SUCCESSFULLY!")
print("=" * 70)

# List created files
created_files = []
for filename in os.listdir('.'):
    if timestamp in filename and (filename.endswith('.csv') or filename.endswith('.xlsx')):
        file_size = os.path.getsize(filename)
        created_files.append((filename, file_size))

if created_files:
    print("\n📁 Created files:")
    total_size = 0
    for filename, size in created_files:
        size_mb = size / (1024 * 1024)
        total_size += size
        print(f"  📄 {filename} ({size_mb:.2f} MB)")
    
    print(f"\n📊 Summary:")
    print(f"  • Total files: {len(created_files)}")
    print(f"  • Total size: {total_size / (1024 * 1024):.2f} MB")
    print(f"  • Timestamp: {timestamp}")

print("\n🚀 Next Steps:")
print("  1. 📊 Open Excel files to explore the data")
print("  2. 📈 Use CSV files for data analysis or machine learning")
print("  3. 🤖 Consider running sentiment analysis on review content")
print("  4. 📉 Create visualizations and trend analysis")
print("  5. 🔍 Perform competitive analysis between apps")

print("\n💡 Tips:")
print("  • Use the 'Reviews' sheet for sentiment analysis")
print("  • Check the 'Summary' sheet for quick insights")
print("  • Filter reviews by app_id for individual app analysis")
print("  • Use review_length and word_count for text analysis")

print("\n✨ Happy analyzing!")