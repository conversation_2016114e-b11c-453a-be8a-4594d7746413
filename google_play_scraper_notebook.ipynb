{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Google Play Store Multi-App Scraper\n", "\n", "This notebook scrapes app information and user reviews from Google Play Store for multiple apps and saves the data to CSV and XLSX formats.\n", "\n", "## Target Apps:\n", "- WhatsApp (`com.whatsapp`)\n", "- Facebook (`com.facebook.katana`)\n", "- Instagram (`com.instagram.android`)\n", "- Snapchat (`com.snapchat.android`)\n", "- Spotify (`com.spotify.music`)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Libraries imported successfully!\n", "📅 Current time: 2025-07-03 11:32:37.149047\n"]}], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "from google_play_scraper import app, reviews, Sort\n", "import time\n", "import json\n", "from datetime import datetime\n", "import os\n", "from typing import List, Dict, Tuple\n", "from tqdm import tqdm\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"✅ Libraries imported successfully!\")\n", "print(f\"📅 Current time: {datetime.now()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 Target: 5 apps\n", "📊 Reviews per app: 1000\n", "🌍 Country: id, Language: jv\n", "📱 Apps: ['com.whatsapp', 'com.facebook.katana', 'com.instagram.android', 'com.snapchat.android', 'com.spotify.music']\n"]}], "source": ["# Configuration\n", "app_ids = [\n", "    'com.whatsapp',\n", "    'com.facebook.katana',\n", "    'com.instagram.android',\n", "    'com.snapchat.android',\n", "    'com.spotify.music'\n", "]\n", "\n", "COUNTRY = 'id'  # Indonesia\n", "LANG = 'id'    # Indonesian\n", "REVIEWS_PER_APP = 1000  # Number of reviews to scrape per app\n", "\n", "print(f\"🎯 Target: {len(app_ids)} apps\")\n", "print(f\"📊 Reviews per app: {REVIEWS_PER_APP}\")\n", "print(f\"🌍 Country: {COUNTRY}, Language: {LANG}\")\n", "print(f\"📱 Apps: {app_ids}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Define Helper Functions"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Helper functions defined!\n"]}], "source": ["def get_app_info(app_id: str, country: str = 'id', lang: str = 'id') -> Dict:\n", "    \"\"\"\n", "    Get detailed app information from Google Play Store.\n", "    \"\"\"\n", "    try:\n", "        app_info = app(app_id, lang=lang, country=country)\n", "        \n", "        return {\n", "            'app_id': app_id,\n", "            'title': app_info.get('title', ''),\n", "            'developer': app_info.get('developer', ''),\n", "            'developer_id': app_info.get('developerId', ''),\n", "            'category': app_info.get('genre', ''),\n", "            'rating': app_info.get('score', 0),\n", "            'rating_count': app_info.get('ratings', 0),\n", "            'installs': app_info.get('installs', ''),\n", "            'price': app_info.get('price', 0),\n", "            'free': app_info.get('free', True),\n", "            'size': app_info.get('size', ''),\n", "            'min_android': app_info.get('minInstalls', ''),\n", "            'content_rating': app_info.get('contentRating', ''),\n", "            'description': app_info.get('description', ''),\n", "            'summary': app_info.get('summary', ''),\n", "            'updated': app_info.get('updated', ''),\n", "            'version': app_info.get('version', ''),\n", "            'recent_changes': app_info.get('recentChanges', ''),\n", "            'scraped_at': datetime.now().isoformat()\n", "        }\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error getting app info for {app_id}: {e}\")\n", "        return {\n", "            'app_id': app_id,\n", "            'error': str(e),\n", "            'scraped_at': datetime.now().isoformat()\n", "        }\n", "\n", "def scrape_app_reviews(app_id: str, count: int = 1000, country: str = 'id', lang: str = 'id') -> List[Dict]:\n", "    \"\"\"\n", "    Scrape reviews for a specific app.\n", "    \"\"\"\n", "    print(f\"\\n📱 Scraping {count} reviews for {app_id}...\")\n", "    \n", "    try:\n", "        all_reviews = []\n", "        continuation_token = None\n", "        batch_size = 200\n", "        \n", "        with tqdm(total=count, desc=f\"Reviews for {app_id}\") as pbar:\n", "            while len(all_reviews) < count:\n", "                try:\n", "                    result, continuation_token = reviews(\n", "                        app_id,\n", "                        lang=lang,\n", "                        country=country,\n", "                        sort=Sort.NEWEST,\n", "                        count=min(batch_size, count - len(all_reviews)),\n", "                        continuation_token=continuation_token\n", "                    )\n", "                    \n", "                    if not result:\n", "                        print(f\"⚠️ No more reviews available for {app_id}\")\n", "                        break\n", "                    \n", "                    # Add metadata to each review\n", "                    for review in result:\n", "                        review['app_id'] = app_id\n", "                        review['scraped_at'] = datetime.now().isoformat()\n", "                    \n", "                    all_reviews.extend(result)\n", "                    pbar.update(len(result))\n", "                    \n", "                    time.sleep(1)  # Rate limiting\n", "                    \n", "                except Exception as e:\n", "                    print(f\"❌ Error scraping batch for {app_id}: {e}\")\n", "                    break\n", "        \n", "        print(f\"✅ Successfully scraped {len(all_reviews)} reviews for {app_id}\")\n", "        return all_reviews\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error scraping reviews for {app_id}: {e}\")\n", "        return []\n", "\n", "print(\"✅ Helper functions defined!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Sc<PERSON>e App Information"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Scraping app information...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Getting app info:   0%|          | 0/5 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ WhatsApp Messenger - Rating: 4.3623743⭐\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Getting app info:  20%|██        | 1/5 [00:01<00:07,  1.90s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Facebook - Rating: 4.619019⭐\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Getting app info:  40%|████      | 2/5 [00:03<00:05,  1.91s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Instagram - Rating: 4.117008⭐\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Getting app info:  60%|██████    | 3/5 [00:05<00:03,  1.92s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Snapchat - Rating: 4.240867⭐\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Getting app info:  80%|████████  | 4/5 [00:07<00:01,  1.91s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Spotify: Music dan Podcast - Rating: 4.471803⭐\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Getting app info: 100%|██████████| 5/5 [00:09<00:00,  1.92s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 App information collected for 5 apps\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>app_id</th>\n", "      <th>title</th>\n", "      <th>developer</th>\n", "      <th>rating</th>\n", "      <th>rating_count</th>\n", "      <th>installs</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>com.whatsapp</td>\n", "      <td>WhatsApp Messenger</td>\n", "      <td>WhatsApp LLC</td>\n", "      <td>4.362374</td>\n", "      <td>209300476</td>\n", "      <td>10.000.000.000+</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>com.facebook.katana</td>\n", "      <td>Facebook</td>\n", "      <td>Meta Platforms, Inc.</td>\n", "      <td>4.619019</td>\n", "      <td>169113269</td>\n", "      <td>10.000.000.000+</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>com.instagram.android</td>\n", "      <td>Instagram</td>\n", "      <td>Instagram</td>\n", "      <td>4.117008</td>\n", "      <td>163289927</td>\n", "      <td>5.000.000.000+</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>com.snapchat.android</td>\n", "      <td>Snapchat</td>\n", "      <td>Snap Inc</td>\n", "      <td>4.240867</td>\n", "      <td>37934337</td>\n", "      <td>1.000.000.000+</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>com.spotify.music</td>\n", "      <td>Spotify: Music dan Podcast</td>\n", "      <td>Spotify AB</td>\n", "      <td>4.471803</td>\n", "      <td>33682868</td>\n", "      <td>1.000.000.000+</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  app_id                       title             developer  \\\n", "0           com.whatsapp          WhatsApp Messenger          WhatsApp LLC   \n", "1    com.facebook.katana                    Facebook  Meta Platforms, Inc.   \n", "2  com.instagram.android                   Instagram             Instagram   \n", "3   com.snapchat.android                    Snapchat              Snap Inc   \n", "4      com.spotify.music  Spotify: Music dan Podcast            Spotify AB   \n", "\n", "     rating  rating_count         installs  \n", "0  4.362374     209300476  10.000.000.000+  \n", "1  4.619019     169113269  10.000.000.000+  \n", "2  4.117008     163289927   5.000.000.000+  \n", "3  4.240867      37934337   1.000.000.000+  \n", "4  4.471803      33682868   1.000.000.000+  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Scrape app information\n", "print(\"🔍 Scraping app information...\")\n", "apps_info = []\n", "\n", "for app_id in tqdm(app_ids, desc=\"Getting app info\"):\n", "    info = get_app_info(app_id, COUNTRY, LANG)\n", "    apps_info.append(info)\n", "    \n", "    if 'error' not in info:\n", "        print(f\"✅ {info.get('title', app_id)} - Rating: {info.get('rating', 'N/A')}⭐\")\n", "    else:\n", "        print(f\"❌ Failed to get info for {app_id}\")\n", "    \n", "    time.sleep(1)  # Rate limiting\n", "\n", "# Convert to DataFrame\n", "apps_df = pd.DataFrame(apps_info)\n", "print(f\"\\n📊 App information collected for {len(apps_df)} apps\")\n", "\n", "# Display results\n", "if not apps_df.empty:\n", "    display(apps_df[['app_id', 'title', 'developer', 'rating', 'rating_count', 'installs']].head())\n", "else:\n", "    print(\"❌ No app information collected\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Scrape User Reviews"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💬 Starting review scraping process...\n", "\n", "📱 Scraping 1000 reviews for com.whatsapp...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Reviews for com.whatsapp: 100%|██████████| 1000/1000 [00:08<00:00, 121.74it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Successfully scraped 1000 reviews for com.whatsapp\n", "📈 Total reviews collected so far: 1000\n", "\n", "📱 Scraping 1000 reviews for com.facebook.katana...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Reviews for com.facebook.katana: 100%|██████████| 1000/1000 [00:07<00:00, 125.03it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Successfully scraped 1000 reviews for com.facebook.katana\n", "📈 Total reviews collected so far: 2000\n", "\n", "📱 Scraping 1000 reviews for com.instagram.android...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Reviews for com.instagram.android: 100%|██████████| 1000/1000 [00:07<00:00, 130.06it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Successfully scraped 1000 reviews for com.instagram.android\n", "📈 Total reviews collected so far: 3000\n", "\n", "📱 Scraping 1000 reviews for com.snapchat.android...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Reviews for com.snapchat.android: 100%|██████████| 1000/1000 [00:08<00:00, 120.88it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Successfully scraped 1000 reviews for com.snapchat.android\n", "📈 Total reviews collected so far: 4000\n", "\n", "📱 Scraping 1000 reviews for com.spotify.music...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Reviews for com.spotify.music: 100%|██████████| 1000/1000 [00:07<00:00, 140.33it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Successfully scraped 1000 reviews for com.spotify.music\n", "📈 Total reviews collected so far: 5000\n", "\n", "🎉 Completed! Total reviews scraped: 5000\n"]}], "source": ["# Scrape reviews for all apps\n", "print(\"💬 Starting review scraping process...\")\n", "all_reviews = []\n", "\n", "for app_id in app_ids:\n", "    app_reviews = scrape_app_reviews(app_id, REVIEWS_PER_APP, COUNTRY, LANG)\n", "    all_reviews.extend(app_reviews)\n", "    print(f\"📈 Total reviews collected so far: {len(all_reviews)}\")\n", "    \n", "    # Longer pause between apps to avoid rate limiting\n", "    time.sleep(3)\n", "\n", "print(f\"\\n🎉 Completed! Total reviews scraped: {len(all_reviews)}\")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Reviews DataFrame created with 5000 rows\n", "📋 Columns: ['reviewId', 'userName', 'userImage', 'content', 'score', 'thumbsUpCount', 'reviewCreatedVersion', 'at', 'replyContent', 'repliedAt', 'appVersion', 'app_id', 'scraped_at', 'review_length', 'word_count']\n", "\n", "📱 Reviews by app:\n", "  • WhatsApp Messenger: 1000 reviews\n", "  • Facebook: 1000 reviews\n", "  • Instagram: 1000 reviews\n", "  • Snapchat: 1000 reviews\n", "  • Spotify: Music dan Podcast: 1000 reviews\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>app_id</th>\n", "      <th>userName</th>\n", "      <th>score</th>\n", "      <th>content</th>\n", "      <th>at</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>com.whatsapp</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>4</td>\n", "      <td>kurang baik</td>\n", "      <td>2025-07-02 11:30:43</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>com.whatsapp</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>5</td>\n", "      <td>Ok</td>\n", "      <td>2025-07-02 11:29:46</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>com.whatsapp</td>\n", "      <td>zergandara ZerganAnjay</td>\n", "      <td>1</td>\n", "      <td>sekarang wa gak semulus dlu, kalo dlu kuota ab...</td>\n", "      <td>2025-07-02 11:29:36</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>com.whatsapp</td>\n", "      <td>setia junfrend jaya zega</td>\n", "      <td>1</td>\n", "      <td>ini kenapa tidak bisa didownload wa nya padaha...</td>\n", "      <td>2025-07-02 11:29:31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>com.whatsapp</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>4</td>\n", "      <td>saya kurangi 1⭐ karena beberapa hari ini saya ...</td>\n", "      <td>2025-07-02 11:28:57</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         app_id                   userName  score  \\\n", "0  com.whatsapp                    Turwono      4   \n", "1  com.whatsapp               <PERSON><PERSON>      5   \n", "2  com.whatsapp     zergandara ZerganAnjay      1   \n", "3  com.whatsapp   setia junfrend jaya zega      1   \n", "4  com.whatsapp  <PERSON><PERSON>      4   \n", "\n", "                                             content                  at  \n", "0                                        kurang baik 2025-07-02 11:30:43  \n", "1                                                 Ok 2025-07-02 11:29:46  \n", "2  se<PERSON>ng wa gak semulus dlu, kalo dlu kuota ab... 2025-07-02 11:29:36  \n", "3  ini kenapa tidak bisa didownload wa nya padaha... 2025-07-02 11:29:31  \n", "4  saya kurangi 1⭐ karena beberapa hari ini saya ... 2025-07-02 11:28:57  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Process reviews data\n", "if all_reviews:\n", "    reviews_df = pd.DataFrame(all_reviews)\n", "    \n", "    # Add additional features\n", "    reviews_df['review_length'] = reviews_df['content'].str.len()\n", "    reviews_df['word_count'] = reviews_df['content'].str.split().str.len()\n", "    \n", "    # Convert date columns\n", "    reviews_df['at'] = pd.to_datetime(reviews_df['at'])\n", "    \n", "    print(f\"📊 Reviews DataFrame created with {len(reviews_df)} rows\")\n", "    print(f\"📋 Columns: {list(reviews_df.columns)}\")\n", "    \n", "    # Show summary by app\n", "    print(\"\\n📱 Reviews by app:\")\n", "    app_review_counts = reviews_df['app_id'].value_counts()\n", "    for app_id, count in app_review_counts.items():\n", "        app_name = apps_df[apps_df['app_id'] == app_id]['title'].iloc[0] if not apps_df.empty else app_id\n", "        print(f\"  • {app_name}: {count} reviews\")\n", "    \n", "    # Display sample\n", "    display(reviews_df[['app_id', 'userName', 'score', 'content', 'at']].head())\n", "else:\n", "    print(\"❌ No reviews were scraped!\")\n", "    reviews_df = pd.DataFrame()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Data Analysis and Summary"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "📊 GOOGLE PLAY STORE SCRAPING SUMMARY\n", "============================================================\n", "\n", "🎯 Apps scraped: 5\n", "💬 Total reviews: 5000\n", "📅 Date range: 2025-05-13 15:51:29 to 2025-07-02 11:32:29\n", "\n", "📱 App Information Summary:\n", "  • WhatsApp Messenger: 4.4⭐ (209300476 ratings, 10.000.000.000+ installs)\n", "  • Facebook: 4.6⭐ (169113269 ratings, 10.000.000.000+ installs)\n", "  • Instagram: 4.1⭐ (163289927 ratings, 5.000.000.000+ installs)\n", "  • Snapchat: 4.2⭐ (37934337 ratings, 1.000.000.000+ installs)\n", "  • Spotify: Music dan Podcast: 4.5⭐ (33682868 ratings, 1.000.000.000+ installs)\n", "\n", "📊 Review Statistics by App:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Review Count</th>\n", "      <th>Avg Rating</th>\n", "      <th>Avg Length</th>\n", "      <th>Avg Words</th>\n", "    </tr>\n", "    <tr>\n", "      <th>app_id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>com.facebook.katana</th>\n", "      <td>1000</td>\n", "      <td>4.25</td>\n", "      <td>36.78</td>\n", "      <td>5.97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>com.instagram.android</th>\n", "      <td>1000</td>\n", "      <td>4.02</td>\n", "      <td>45.42</td>\n", "      <td>7.49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>com.snapchat.android</th>\n", "      <td>1000</td>\n", "      <td>4.29</td>\n", "      <td>40.71</td>\n", "      <td>7.07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>com.spotify.music</th>\n", "      <td>1000</td>\n", "      <td>4.39</td>\n", "      <td>38.84</td>\n", "      <td>6.52</td>\n", "    </tr>\n", "    <tr>\n", "      <th>com.whatsapp</th>\n", "      <td>1000</td>\n", "      <td>3.68</td>\n", "      <td>50.20</td>\n", "      <td>8.44</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                       Review Count  Avg Rating  Avg Length  Avg Words\n", "app_id                                                                \n", "com.facebook.katana            1000        4.25       36.78       5.97\n", "com.instagram.android          1000        4.02       45.42       7.49\n", "com.snapchat.android           1000        4.29       40.71       7.07\n", "com.spotify.music              1000        4.39       38.84       6.52\n", "com.whatsapp                   1000        3.68       50.20       8.44"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "⭐ Overall Rating Distribution:\n", "  ⭐ (1): 717 reviews (14.3%)\n", "  ⭐⭐ (2): 176 reviews (3.5%)\n", "  ⭐⭐⭐ (3): 255 reviews (5.1%)\n", "  ⭐⭐⭐⭐ (4): 452 reviews (9.0%)\n", "  ⭐⭐⭐⭐⭐ (5): 3400 reviews (68.0%)\n"]}], "source": ["# Create summary statistics\n", "if not reviews_df.empty and not apps_df.empty:\n", "    print(\"=\" * 60)\n", "    print(\"📊 GOOGLE PLAY STORE SCRAPING SUMMARY\")\n", "    print(\"=\" * 60)\n", "    \n", "    print(f\"\\n🎯 Apps scraped: {len(apps_df)}\")\n", "    print(f\"💬 Total reviews: {len(reviews_df)}\")\n", "    print(f\"📅 Date range: {reviews_df['at'].min()} to {reviews_df['at'].max()}\")\n", "    \n", "    print(\"\\n📱 App Information Summary:\")\n", "    for _, app in apps_df.iterrows():\n", "        if 'title' in app and 'rating' in app and 'error' not in app:\n", "            rating_count = app.get('rating_count', 'N/A')\n", "            installs = app.get('installs', 'N/A')\n", "            print(f\"  • {app['title']}: {app['rating']:.1f}⭐ ({rating_count} ratings, {installs} installs)\")\n", "    \n", "    print(\"\\n📊 Review Statistics by App:\")\n", "    review_stats = reviews_df.groupby('app_id').agg({\n", "        'score': ['count', 'mean'],\n", "        'review_length': 'mean',\n", "        'word_count': 'mean'\n", "    }).round(2)\n", "    \n", "    review_stats.columns = ['Review Count', 'Avg Rating', 'Avg Length', 'Avg Words']\n", "    display(review_stats)\n", "    \n", "    print(\"\\n⭐ Overall Rating Distribution:\")\n", "    rating_dist = reviews_df['score'].value_counts().sort_index()\n", "    for rating, count in rating_dist.items():\n", "        percentage = (count / len(reviews_df)) * 100\n", "        stars = \"⭐\" * rating\n", "        print(f\"  {stars} ({rating}): {count} reviews ({percentage:.1f}%)\")\n", "else:\n", "    print(\"❌ No data available for summary\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Export Data to CSV and XLSX"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📅 Export timestamp: 20250703_113341\n", "✅ App information saved to: google_play_apps_info_20250703_113341.csv\n", "✅ App information saved to: google_play_apps_info_20250703_113341.xlsx\n", "✅ Reviews saved to: google_play_reviews_20250703_113341.csv\n", "✅ Reviews saved to: google_play_reviews_20250703_113341.xlsx\n", "\n", "📁 All data exported with timestamp: 20250703_113341\n"]}], "source": ["# Create timestamp for file naming\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "print(f\"📅 Export timestamp: {timestamp}\")\n", "\n", "# Export app information\n", "if not apps_df.empty:\n", "    # CSV export\n", "    apps_csv_filename = f\"google_play_apps_info_{timestamp}.csv\"\n", "    apps_df.to_csv(apps_csv_filename, index=False, encoding='utf-8')\n", "    print(f\"✅ App information saved to: {apps_csv_filename}\")\n", "    \n", "    # XLSX export\n", "    try:\n", "        apps_xlsx_filename = f\"google_play_apps_info_{timestamp}.xlsx\"\n", "        apps_df.to_excel(apps_xlsx_filename, index=False, engine='openpyxl')\n", "        print(f\"✅ App information saved to: {apps_xlsx_filename}\")\n", "    except ImportError:\n", "        print(\"⚠️ openpyxl not installed. Install with: pip install openpyxl\")\n", "else:\n", "    print(\"❌ No app information to export\")\n", "\n", "# Export reviews\n", "if not reviews_df.empty:\n", "    # CSV export\n", "    reviews_csv_filename = f\"google_play_reviews_{timestamp}.csv\"\n", "    reviews_df.to_csv(reviews_csv_filename, index=False, encoding='utf-8')\n", "    print(f\"✅ Reviews saved to: {reviews_csv_filename}\")\n", "    \n", "    # XLSX export\n", "    try:\n", "        reviews_xlsx_filename = f\"google_play_reviews_{timestamp}.xlsx\"\n", "        reviews_df.to_excel(reviews_xlsx_filename, index=False, engine='openpyxl')\n", "        print(f\"✅ Reviews saved to: {reviews_xlsx_filename}\")\n", "    except ImportError:\n", "        print(\"⚠️ openpyxl not installed. Install with: pip install openpyxl\")\n", "else:\n", "    print(\"❌ No reviews to export\")\n", "\n", "print(f\"\\n📁 All data exported with timestamp: {timestamp}\")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ App information added to sheet 'App_Information'\n", "✅ Reviews added to sheet 'Reviews'\n", "✅ Summary added to sheet 'Summary'\n", "\n", "🎉 Combined Excel file created: google_play_complete_data_20250703_113341.xlsx\n", "   📊 Contains: App Information, Reviews, and Summary sheets\n"]}], "source": ["# Create a combined Excel file with multiple sheets\n", "if not apps_df.empty or not reviews_df.empty:\n", "    try:\n", "        combined_filename = f\"google_play_complete_data_{timestamp}.xlsx\"\n", "        \n", "        with pd.ExcelWriter(combined_filename, engine='openpyxl') as writer:\n", "            if not apps_df.empty:\n", "                apps_df.to_excel(writer, sheet_name='App_Information', index=False)\n", "                print(f\"✅ App information added to sheet 'App_Information'\")\n", "            \n", "            if not reviews_df.empty:\n", "                reviews_df.to_excel(writer, sheet_name='Reviews', index=False)\n", "                print(f\"✅ Reviews added to sheet 'Reviews'\")\n", "                \n", "                # Create summary sheet\n", "                summary_data = []\n", "                \n", "                # Overall statistics\n", "                summary_data.append(['Metric', 'Value'])\n", "                summary_data.append(['Total Apps', len(apps_df) if not apps_df.empty else 0])\n", "                summary_data.append(['Total Reviews', len(reviews_df)])\n", "                summary_data.append(['Date Range Start', reviews_df['at'].min().strftime('%Y-%m-%d')])\n", "                summary_data.append(['Date Range End', reviews_df['at'].max().strftime('%Y-%m-%d')])\n", "                summary_data.append(['Average Rating', round(reviews_df['score'].mean(), 2)])\n", "                summary_data.append(['', ''])  # Empty row\n", "                \n", "                # Rating distribution\n", "                summary_data.append(['Rating Distribution', ''])\n", "                rating_dist = reviews_df['score'].value_counts().sort_index()\n", "                for rating, count in rating_dist.items():\n", "                    percentage = (count / len(reviews_df)) * 100\n", "                    summary_data.append([f'{rating} Stars', f'{count} ({percentage:.1f}%)'])\n", "                \n", "                summary_df = pd.DataFrame(summary_data)\n", "                summary_df.to_excel(writer, sheet_name='Summary', index=False, header=False)\n", "                print(f\"✅ Summary added to sheet 'Summary'\")\n", "        \n", "        print(f\"\\n🎉 Combined Excel file created: {combined_filename}\")\n", "        print(f\"   📊 Contains: App Information, Reviews, and Summary sheets\")\n", "        \n", "    except ImportError:\n", "        print(\"⚠️ openpyxl not installed. Combined Excel file not created.\")\n", "        print(\"   Install with: pip install openpyxl\")\n", "    except Exception as e:\n", "        print(f\"❌ Error creating combined Excel file: {e}\")\n", "else:\n", "    print(\"❌ No data available to create combined file\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Final Summary and Next Steps"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["======================================================================\n", "🎉 SCRAPING COMPLETED SUCCESSFULLY!\n", "======================================================================\n", "\n", "📁 Created files:\n", "  📄 google_play_apps_info_20250703_113341.csv (0.01 MB)\n", "  📄 google_play_apps_info_20250703_113341.xlsx (0.01 MB)\n", "  📄 google_play_complete_data_20250703_113341.xlsx (0.85 MB)\n", "  📄 google_play_reviews_20250703_113341.csv (1.39 MB)\n", "  📄 google_play_reviews_20250703_113341.xlsx (0.84 MB)\n", "\n", "📊 Summary:\n", "  • Total files: 5\n", "  • Total size: 3.10 MB\n", "  • Timestamp: 20250703_113341\n", "\n", "🚀 Next Steps:\n", "  1. 📊 Open Excel files to explore the data\n", "  2. 📈 Use CSV files for data analysis or machine learning\n", "  3. 🤖 Consider running sentiment analysis on review content\n", "  4. 📉 Create visualizations and trend analysis\n", "  5. 🔍 Perform competitive analysis between apps\n", "\n", "💡 Tips:\n", "  • Use the 'Reviews' sheet for sentiment analysis\n", "  • Check the 'Summary' sheet for quick insights\n", "  • Filter reviews by app_id for individual app analysis\n", "  • Use review_length and word_count for text analysis\n", "\n", "✨ Happy analyzing!\n"]}], "source": ["# Display final summary and file information\n", "print(\"=\" * 70)\n", "print(\"🎉 SCRAPING COMPLETED SUCCESSFULLY!\")\n", "print(\"=\" * 70)\n", "\n", "# List created files\n", "created_files = []\n", "for filename in os.listdir('.'):\n", "    if timestamp in filename and (filename.endswith('.csv') or filename.endswith('.xlsx')):\n", "        file_size = os.path.getsize(filename)\n", "        created_files.append((filename, file_size))\n", "\n", "if created_files:\n", "    print(\"\\n📁 Created files:\")\n", "    total_size = 0\n", "    for filename, size in created_files:\n", "        size_mb = size / (1024 * 1024)\n", "        total_size += size\n", "        print(f\"  📄 {filename} ({size_mb:.2f} MB)\")\n", "    \n", "    print(f\"\\n📊 Summary:\")\n", "    print(f\"  • Total files: {len(created_files)}\")\n", "    print(f\"  • Total size: {total_size / (1024 * 1024):.2f} MB\")\n", "    print(f\"  • Timestamp: {timestamp}\")\n", "\n", "print(\"\\n🚀 Next Steps:\")\n", "print(\"  1. 📊 Open Excel files to explore the data\")\n", "print(\"  2. 📈 Use CSV files for data analysis or machine learning\")\n", "print(\"  3. 🤖 Consider running sentiment analysis on review content\")\n", "print(\"  4. 📉 Create visualizations and trend analysis\")\n", "print(\"  5. 🔍 Perform competitive analysis between apps\")\n", "\n", "print(\"\\n💡 Tips:\")\n", "print(\"  • Use the 'Reviews' sheet for sentiment analysis\")\n", "print(\"  • Check the 'Summary' sheet for quick insights\")\n", "print(\"  • Filter reviews by app_id for individual app analysis\")\n", "print(\"  • Use review_length and word_count for text analysis\")\n", "\n", "print(\"\\n✨ Happy analyzing!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}