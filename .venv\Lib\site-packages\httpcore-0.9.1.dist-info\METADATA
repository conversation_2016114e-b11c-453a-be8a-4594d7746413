Metadata-Version: 2.1
Name: httpcore
Version: 0.9.1
Summary: A minimal low-level HTTP client.
Home-page: https://github.com/encode/httpcore
Author: <PERSON>
Author-email: <EMAIL>
License: BSD
Project-URL: Documentation, https://www.encode.io/httpcore
Project-URL: Source, https://github.com/encode/httpcore
Platform: UNKNOWN
Classifier: Development Status :: 3 - Alpha
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Topic :: Internet :: WWW/HTTP
Classifier: Framework :: AsyncIO
Classifier: Framework :: Trio
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3 :: Only
Requires-Python: >=3.6
Description-Content-Type: text/markdown
Requires-Dist: h11 (<0.10,>=0.8)
Requires-Dist: h2 (==3.*)
Requires-Dist: sniffio (==1.*)

# HTTP Core

[![Test Suite](https://github.com/encode/httpcore/workflows/Test%20Suite/badge.svg)](https://github.com/encode/httpcore/actions)
[![Package version](https://badge.fury.io/py/httpcore.svg)](https://pypi.org/project/httpcore/)

> *Do one thing, and do it well.*

The HTTP Core package provides a minimal low-level HTTP client, which does
one thing only. Sending HTTP requests.

It does not provide any high level model abstractions over the API,
does not handle redirects, multipart uploads, building authentication headers,
transparent HTTP caching, URL parsing, session cookie handling,
content or charset decoding, handling JSON, environment based configuration
defaults, or any of that Jazz.

Some things HTTP Core does do:

* Sending HTTP requests.
* Provides both sync and async interfaces.
* Supports HTTP/1.1 and HTTP/2.
* Async backend support for `asyncio` and `trio`.
* Automatic connection pooling.
* HTTP(S) proxy support.

## Quickstart

Here's an example of making an HTTP GET request using `httpcore`...

```python
async with httpcore.AsyncConnectionPool() as http:
    http_version, status_code, reason_phrase, headers, stream = await http.request(
        method=b'GET',
        url=(b'https', b'example.org', 443, b'/'),
    )

    try:
        body = b''.join(chunk async for chunk in stream)
    finally:
        await stream.close()

    print(status_code, body)
```

## Motivation

You probably don't want to be using HTTP Core directly. It might make sense if
you're writing something like a proxy service in Python, and you just want
something at the lowest possible level, but more typically you'll want to use
a higher level client library, such as `httpx`.

The motivation for `httpcore` is:

* To provide a reusable low-level client library, that other packages can then build on top of.
* To provide a *really clear interface split* between the networking code and client logic,
  so that each is easier to understand and reason about in isolation.


# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/).

## 0.9.1 (May 27th, 2020)

### Fixed

- Proper host resolution for sync case, including IPv6 support. (Pull #97)
- Close outstanding connections when connection pool is closed. (Pull #98)

## 0.9.0 (May 21th, 2020)

### Changed

- URL port becomes an `Optional[int]` instead of `int`. (Pull #92)

### Fixed

- Honor HTTP/2 max concurrent streams settings. (Pull #89, #90)
- Remove incorrect debug log. (Pull #83)

## 0.8.4 (May 11th, 2020)

### Added

- Logging via HTTPCORE_LOG_LEVEL and HTTPX_LOG_LEVEL environment variables
and TRACE level logging. (Pull #79)

### Fixed

- Reuse of connections on HTTP/2 in close concurrency situations. (Pull #81)

## 0.8.3 (May 6rd, 2020)

### Fixed

- Include `Host` and `Accept` headers on proxy "CONNECT" requests.
- De-duplicate any headers also contained in proxy_headers.
- HTTP/2 flag not being passed down to proxy connections.

## 0.8.2 (May 3rd, 2020)

### Fixed

- Fix connections using proxy forwarding requests not being added to the
connection pool properly. (Pull #70)

## 0.8.1 (April 30th, 2020)

### Changed

- Allow inherintance of both `httpcore.AsyncByteStream`, `httpcore.SyncByteStream` without type conflicts.

## 0.8.0 (April 30th, 2020)

### Fixed

- Fixed tunnel proxy support.

### Added

- New `TimeoutException` base class.

## 0.7.0 (March 5th, 2020)

- First integration with HTTPX.


