../../Scripts/nltk.exe,sha256=uuUGRDs8Wo7KMglqnsDsDoVGV1fjxkwKDPPai5_MsZU,108411
nltk-3.8.1.dist-info/AUTHORS.md,sha256=lwegiKq14iCouEfpgu85VSAWadP2X1MkLhUsgYBfPOI,7628
nltk-3.8.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
nltk-3.8.1.dist-info/LICENSE.txt,sha256=Pd-b5cKP4n2tFDpdx27qJSIq0d1ok0oEcGTlbtL6QMU,11560
nltk-3.8.1.dist-info/METADATA,sha256=CUHc77qyEPWGmH6DiO6622SIsRU2fGENF_LCKUzGEOI,2847
nltk-3.8.1.dist-info/README.md,sha256=_oLlVxk8v-ARv0t4wAyrPKZ8KmLA2y1tlhJ4C3QjRk0,1789
nltk-3.8.1.dist-info/RECORD,,
nltk-3.8.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nltk-3.8.1.dist-info/WHEEL,sha256=ewwEueio1C2XeHTvT17n8dZUJgOvyCWCt0WVNLClP9o,92
nltk-3.8.1.dist-info/entry_points.txt,sha256=SK6SzMicwUtBiwUOmv5P1ZVs0h-xqey6PnRpsUGGx5c,37
nltk-3.8.1.dist-info/top_level.txt,sha256=YoQ-mwqckmTv1Qktmlk5Ylb6lDG77jg5qwoEB7c-pXo,5
nltk/VERSION,sha256=932VxvO5Mh-hWNyZVXsqpdneLGKgY0kKcA4_XVSSvyQ,7
nltk/__init__.py,sha256=RSji7RAoc5vyHzOA6pB6LPpCUxB8o68igd6CrYElOGA,6432
nltk/__pycache__/__init__.cpython-312.pyc,,
nltk/__pycache__/book.cpython-312.pyc,,
nltk/__pycache__/cli.cpython-312.pyc,,
nltk/__pycache__/collections.cpython-312.pyc,,
nltk/__pycache__/collocations.cpython-312.pyc,,
nltk/__pycache__/compat.cpython-312.pyc,,
nltk/__pycache__/data.cpython-312.pyc,,
nltk/__pycache__/decorators.cpython-312.pyc,,
nltk/__pycache__/downloader.cpython-312.pyc,,
nltk/__pycache__/featstruct.cpython-312.pyc,,
nltk/__pycache__/grammar.cpython-312.pyc,,
nltk/__pycache__/help.cpython-312.pyc,,
nltk/__pycache__/internals.cpython-312.pyc,,
nltk/__pycache__/jsontags.cpython-312.pyc,,
nltk/__pycache__/langnames.cpython-312.pyc,,
nltk/__pycache__/lazyimport.cpython-312.pyc,,
nltk/__pycache__/probability.cpython-312.pyc,,
nltk/__pycache__/text.cpython-312.pyc,,
nltk/__pycache__/tgrep.cpython-312.pyc,,
nltk/__pycache__/toolbox.cpython-312.pyc,,
nltk/__pycache__/treeprettyprinter.cpython-312.pyc,,
nltk/__pycache__/treetransforms.cpython-312.pyc,,
nltk/__pycache__/util.cpython-312.pyc,,
nltk/__pycache__/wsd.cpython-312.pyc,,
nltk/app/__init__.py,sha256=xGZbbDC3xv67XnHHusxZmCPMNqj07BM9W6ZSlkWn9eQ,1578
nltk/app/__pycache__/__init__.cpython-312.pyc,,
nltk/app/__pycache__/chartparser_app.cpython-312.pyc,,
nltk/app/__pycache__/chunkparser_app.cpython-312.pyc,,
nltk/app/__pycache__/collocations_app.cpython-312.pyc,,
nltk/app/__pycache__/concordance_app.cpython-312.pyc,,
nltk/app/__pycache__/nemo_app.cpython-312.pyc,,
nltk/app/__pycache__/rdparser_app.cpython-312.pyc,,
nltk/app/__pycache__/srparser_app.cpython-312.pyc,,
nltk/app/__pycache__/wordfreq_app.cpython-312.pyc,,
nltk/app/__pycache__/wordnet_app.cpython-312.pyc,,
nltk/app/chartparser_app.py,sha256=8FX3-eJQmB-8LT9k-lQJe_y5dSO3Ly4AD2K_wIs9FuE,88195
nltk/app/chunkparser_app.py,sha256=tbEPtYtccyTcbSCUhFhhbkjCVi_rtel4EKPSogeJOT8,58322
nltk/app/collocations_app.py,sha256=gJBWxNmkUjXQWkruqcOSE8l51Md2fcM3RmZlrJHJZK4,14664
nltk/app/concordance_app.py,sha256=HjLN9ybKbjbKqkhJNUakivwIPojvDxqvwnqb7u07BYE,24882
nltk/app/nemo_app.py,sha256=6ZBJXlJWKWoYnsrEy3Yy6IeFxcy3FNaGWK6QnMYEy4E,12305
nltk/app/rdparser_app.py,sha256=j4tMGNLnwrwkVw3MyMr3-56TXAwAIIEo-v0yWyjDKEQ,37781
nltk/app/srparser_app.py,sha256=UQwxqEPDfSYJTw22SSdp5EUX0QwlbztyH4EYHtBerw0,34401
nltk/app/wordfreq_app.py,sha256=0mzSrNosW3Wh_J_5FdJV8Bq-F_a2x5HRV3iQAI03fnQ,957
nltk/app/wordnet_app.py,sha256=Ut5VU3hzM4inRayi4c5uyNsbAz7YcGW7_q8BBmrJpPs,35574
nltk/book.py,sha256=enAPUeJxxAXY0C60vlmPHCVhUxVY2K2gx3wWPH6tU6k,3912
nltk/ccg/__init__.py,sha256=Gz2z13lWdN_wdcvn78rxJGvU23EKIN_sNm5twz_2nWw,915
nltk/ccg/__pycache__/__init__.cpython-312.pyc,,
nltk/ccg/__pycache__/api.cpython-312.pyc,,
nltk/ccg/__pycache__/chart.cpython-312.pyc,,
nltk/ccg/__pycache__/combinator.cpython-312.pyc,,
nltk/ccg/__pycache__/lexicon.cpython-312.pyc,,
nltk/ccg/__pycache__/logic.cpython-312.pyc,,
nltk/ccg/api.py,sha256=3xzrsFkp0XM_SihDIEODQEMgFh-KYBaCQs4WNStLTgU,10360
nltk/ccg/chart.py,sha256=2lyYNM8PY6AhucRmNetqgylPfKz3Pzn4faAKtkvYuFA,14147
nltk/ccg/combinator.py,sha256=1C5Tqwhp-diD7rHtUfpPbVt4v1a7oPBY0bkHPm52OD4,10633
nltk/ccg/lexicon.py,sha256=9rC11EzdzOVMybBt6TeYdVw4xj73Ufy7NS1cTqtq5sU,9863
nltk/ccg/logic.py,sha256=MEukXOQu6dX-i-irRH3Nko5D2ElpDGjVosdgHPZs8wg,1871
nltk/chat/__init__.py,sha256=4aSic0g0Zwhlxm7PC_t-0JZjChyKcPXTS0hoWTyTvLw,1556
nltk/chat/__pycache__/__init__.cpython-312.pyc,,
nltk/chat/__pycache__/eliza.cpython-312.pyc,,
nltk/chat/__pycache__/iesha.cpython-312.pyc,,
nltk/chat/__pycache__/rude.cpython-312.pyc,,
nltk/chat/__pycache__/suntsu.cpython-312.pyc,,
nltk/chat/__pycache__/util.cpython-312.pyc,,
nltk/chat/__pycache__/zen.cpython-312.pyc,,
nltk/chat/eliza.py,sha256=27GYLQfKpMzsBvPixXQnZHqZSmaI_8H3mAvuPZAUVNw,9626
nltk/chat/iesha.py,sha256=WassBbqcT2LbxZHda7vwcIBeIOfzefZ19cxUGay-NNM,4407
nltk/chat/rude.py,sha256=JMoqOg2_r30pNRwknXWG8qIi_0mm__AnI7tTM1orj2I,3289
nltk/chat/suntsu.py,sha256=dlYCRQ3INyOXbfL0qwyLaq1E-fqIVS8weRk2gOC8tq0,7185
nltk/chat/util.py,sha256=dbgxikuBJGP6YhDPFw_ZYTSsBqpqEV5HU1ipWfj21Bw,4014
nltk/chat/zen.py,sha256=KtZcUzKXlwyfL_tQpa9rtuNB12PAscwaWt2pbvk6GcM,11679
nltk/chunk/__init__.py,sha256=hIssYRWZj_6YmHQOhJe3DRlvqbehf-Y7e6kSy8Sicp0,7597
nltk/chunk/__pycache__/__init__.cpython-312.pyc,,
nltk/chunk/__pycache__/api.cpython-312.pyc,,
nltk/chunk/__pycache__/named_entity.cpython-312.pyc,,
nltk/chunk/__pycache__/regexp.cpython-312.pyc,,
nltk/chunk/__pycache__/util.cpython-312.pyc,,
nltk/chunk/api.py,sha256=-gEfVh1nv3CO-YXV3kTSMNDS4_sbuKnM3xVuTq2oc60,1946
nltk/chunk/named_entity.py,sha256=v__H3Rply3PvrzKRUM2ktkLQcMYJc_14qHFbiKqqaMo,11140
nltk/chunk/regexp.py,sha256=KXfm9-KJNqSRSJFfV5192yErXVuLH2jmOeCJbROkPRU,55980
nltk/chunk/util.py,sha256=Ll5PB0ozF7rwNJtsdM6YiA1zktVLO7MOaQtJDR2Qx4g,21311
nltk/classify/__init__.py,sha256=2s2RPR2IPix1aXumcnpzKSYJ8BzaC-VsKcpVHHZPT0E,4596
nltk/classify/__pycache__/__init__.cpython-312.pyc,,
nltk/classify/__pycache__/api.cpython-312.pyc,,
nltk/classify/__pycache__/decisiontree.cpython-312.pyc,,
nltk/classify/__pycache__/maxent.cpython-312.pyc,,
nltk/classify/__pycache__/megam.cpython-312.pyc,,
nltk/classify/__pycache__/naivebayes.cpython-312.pyc,,
nltk/classify/__pycache__/positivenaivebayes.cpython-312.pyc,,
nltk/classify/__pycache__/rte_classify.cpython-312.pyc,,
nltk/classify/__pycache__/scikitlearn.cpython-312.pyc,,
nltk/classify/__pycache__/senna.cpython-312.pyc,,
nltk/classify/__pycache__/svm.cpython-312.pyc,,
nltk/classify/__pycache__/tadm.cpython-312.pyc,,
nltk/classify/__pycache__/textcat.cpython-312.pyc,,
nltk/classify/__pycache__/util.cpython-312.pyc,,
nltk/classify/__pycache__/weka.cpython-312.pyc,,
nltk/classify/api.py,sha256=PN1b_jw2InZWMNuzMaPSs2PP-f9_7IZfokohkKd0Xro,6625
nltk/classify/decisiontree.py,sha256=HL-V9gcFYX2uYaonc3glQq_CAEqyCxKTb1FnKxkpx8U,13083
nltk/classify/maxent.py,sha256=pJZFnshxF4jfYlY-8zgf3N8P5jVczqxTOCAI6HrVTqA,60921
nltk/classify/megam.py,sha256=4d2NlMAyrXca2TB_phpff41-qY8YZdqx6LrYLL5s0jI,6396
nltk/classify/naivebayes.py,sha256=fahYSKoSAMisUKOjXxkIDsRRU7swLyFMSAloL8mToNU,10713
nltk/classify/positivenaivebayes.py,sha256=WckMp6Olu6x6Ku__NCRVPO2n6WY_AI2yr1y46cy-IgU,7412
nltk/classify/rte_classify.py,sha256=d7BhvcXp-j1ovcbFs0jz2I22nZtn1pBvfT79kpc1xnY,6301
nltk/classify/scikitlearn.py,sha256=_D3TQC-jxEn-eq3Y7Ydc1OczkhIAbednxDpcFpbj99U,5548
nltk/classify/senna.py,sha256=WGne67HygHBl85t4DKqTjWgjILTVOaXoDrQgV7odLm8,6931
nltk/classify/svm.py,sha256=Izn33z8jQhQ70hJdbli-HUc_dly9O2sxMso0v1MZ5dY,525
nltk/classify/tadm.py,sha256=jGR9ga8n1rQUCoRg49kkSyZkJ7thteHc0TAApdtVaVU,3555
nltk/classify/textcat.py,sha256=BeHyxtRXdqAGJtnipjORVrCIjWTHVa0OJm1-WOoMybI,6035
nltk/classify/util.py,sha256=1Puz0ks5SrYXYQ8eJbcJpqRtGdwS1Hji0TMQk70ZCy4,12461
nltk/classify/weka.py,sha256=em2Rij5vMKo5LFZbHWBsPZlDkBb-QOLMMEGIPjSgBoI,12938
nltk/cli.py,sha256=ZdakKKRjRmDn_b3e4TL1UNqaTF4VsLSMgQ4juVWstEM,1897
nltk/cluster/__init__.py,sha256=1mPkvd-mjaRXe0Aha9qx5Gn_Dr39BRovq-qT74bUi54,4361
nltk/cluster/__pycache__/__init__.cpython-312.pyc,,
nltk/cluster/__pycache__/api.cpython-312.pyc,,
nltk/cluster/__pycache__/em.cpython-312.pyc,,
nltk/cluster/__pycache__/gaac.cpython-312.pyc,,
nltk/cluster/__pycache__/kmeans.cpython-312.pyc,,
nltk/cluster/__pycache__/util.cpython-312.pyc,,
nltk/cluster/api.py,sha256=sranVby2NHrTr3vmefGkZgREzzOjlE94MLCUbO63rlU,2162
nltk/cluster/em.py,sha256=uxd6qQ0T1PSE5e_3q41yk66OlkPMX0ws4L0J7ciq1YM,8419
nltk/cluster/gaac.py,sha256=c_2ewAkcLdWLhW0WjUedESoV7I1UPGlNeY_BNhOTQqY,5921
nltk/cluster/kmeans.py,sha256=1Ik_3_pIjCfpgDag0LmP4hi2SQiRIwy8sQqtXFSuHYA,8592
nltk/cluster/util.py,sha256=TVZtWob_8SoZOwG6NtsBPk4fOD40ZVjxnHj9oPa6eC8,10039
nltk/collections.py,sha256=DiSo-vicLp7UQLpDiDTljFwdDLdQVUi7UVAdgublO8A,23673
nltk/collocations.py,sha256=NWC5upNNulRI_FYmCHX0rNeZCkCQxqVXMlLnOC_bwa8,14964
nltk/compat.py,sha256=7f0Eg2_MbidKae8brT_oCuqDSHcfmOskS88Y6-lycmw,1307
nltk/corpus/__init__.py,sha256=qGkuNZ2GIP4qDOR6mMVtJHnC7oMV3kRxiNr8aGFHhfs,17359
nltk/corpus/__pycache__/__init__.cpython-312.pyc,,
nltk/corpus/__pycache__/europarl_raw.cpython-312.pyc,,
nltk/corpus/__pycache__/util.cpython-312.pyc,,
nltk/corpus/europarl_raw.py,sha256=aXMKViytBRbry4J0FrO0P20JTOV2bgjuJQ5hOxFkJ-0,1896
nltk/corpus/reader/__init__.py,sha256=urxkSILuhBlGI9qvsIlhQap6nFKSfkKrYi-rb4LCV5U,6677
nltk/corpus/reader/__pycache__/__init__.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/aligned.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/api.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/bcp47.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/bnc.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/bracket_parse.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/categorized_sents.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/chasen.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/childes.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/chunked.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/cmudict.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/comparative_sents.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/conll.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/crubadan.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/dependency.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/framenet.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/ieer.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/indian.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/ipipan.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/knbc.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/lin.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/markdown.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/mte.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/nkjp.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/nombank.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/nps_chat.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/opinion_lexicon.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/panlex_lite.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/panlex_swadesh.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/pl196x.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/plaintext.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/ppattach.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/propbank.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/pros_cons.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/reviews.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/rte.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/semcor.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/senseval.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/sentiwordnet.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/sinica_treebank.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/string_category.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/switchboard.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/tagged.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/timit.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/toolbox.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/twitter.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/udhr.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/util.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/verbnet.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/wordlist.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/wordnet.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/xmldocs.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/ycoe.cpython-312.pyc,,
nltk/corpus/reader/aligned.py,sha256=OJUpm8HyzqR0e8hPHd5CrAmH1bfQfXV2hZ4KCak0Zzw,5005
nltk/corpus/reader/api.py,sha256=Dhu491gmTJnWSilm6lWdQBN59RM1aIT410mrbiDwD1k,19671
nltk/corpus/reader/bcp47.py,sha256=I27Lj4hMW2IRM62RyXeK9OX-kSeAvEEkoXVWN1HwEiY,8534
nltk/corpus/reader/bnc.py,sha256=AhFjLzhCpgijSGvhhHiB5AT5vmAPRP6LO-bHEXOEXas,9716
nltk/corpus/reader/bracket_parse.py,sha256=1EfRuNmCU9DESWjwalyiDZhQuOwb3IYDjL-aAXlId_U,9619
nltk/corpus/reader/categorized_sents.py,sha256=QLpHrDk3JlbgitkpbfndSXGxfssAyAA5MsnBesFSKis,6221
nltk/corpus/reader/chasen.py,sha256=iVW9xtRtGcr3EpEf_BjbusRcPqRqPzKoFlOJfxdooTM,4699
nltk/corpus/reader/childes.py,sha256=keelogBlaIvHWv1A0Q_tHbzmKyMthB2_YNWXQQmG53g,26105
nltk/corpus/reader/chunked.py,sha256=8foVT90OCSIzv7B99rHsVGSiAX62e4HFc8QyGDqc_4Y,9366
nltk/corpus/reader/cmudict.py,sha256=CCDpjWDzwPMOXKprKxmNtvX_-gUSFahFO-vUdTJbNhU,3366
nltk/corpus/reader/comparative_sents.py,sha256=9YwKIk0xf_EhB6qBepCMOENtxoyvOqwuV_aIjW_WRk4,12069
nltk/corpus/reader/conll.py,sha256=l-pgBQfCiZP90EJo1hnG3TLM9wM5imFvqlt4FfXlDII,22301
nltk/corpus/reader/crubadan.py,sha256=FLorWHpsugP1Z3bgkukr51baIw_FvAdNqRKIcPeURL4,3627
nltk/corpus/reader/dependency.py,sha256=nAdjT0H-HV9JW6iUMNe4cW8RR5rdA8IB8ZQ5IQY9aIc,3890
nltk/corpus/reader/framenet.py,sha256=n59uoGpOSFIapeYnkbPmCamuslARpoa9q8773DkxB5Y,134791
nltk/corpus/reader/ieer.py,sha256=x6CnLllJTOQYpwGKxjsbtGjej-rxVCEKb4HUyUbmCAw,3802
nltk/corpus/reader/indian.py,sha256=GQMsNmZwoWvSS4bdeBT5guNUyU8-LErUoMfuT2ukJb0,3014
nltk/corpus/reader/ipipan.py,sha256=1yJ5cl7AwX9SFmJs07HsuW-eZHp_YkTL-n1nRhadzgw,13092
nltk/corpus/reader/knbc.py,sha256=rKg0SDmeQCUaSc3SOr97_IfM5FGJjjvTCdFDn1Vnvn8,5787
nltk/corpus/reader/lin.py,sha256=1gx48Odd8WJ9PpoF6_f5UjmqJ5j22Rsu8GMraepu7Mg,6654
nltk/corpus/reader/markdown.py,sha256=Y9AeB3F1gbUuEK7HNjR6iwHDAYeneI5XH1rMkw_ubWQ,12028
nltk/corpus/reader/mte.py,sha256=8p5iQIJOuxu9MOWa1xH1Iijj9J9KmcRUWh2vmcWSOqQ,14385
nltk/corpus/reader/nkjp.py,sha256=9npzIMG-tOJjb1NpiwDvH1uXXQ5eBEq8xp_KeQ5jD10,16332
nltk/corpus/reader/nombank.py,sha256=v-smV3YSQfl54cF-NM0St9dodOKawPL6JGsMv5tvI4g,16247
nltk/corpus/reader/nps_chat.py,sha256=tjp98O7FNsyoxXenMT5OW0XaJN1RciICJfeekN6AWa8,2940
nltk/corpus/reader/opinion_lexicon.py,sha256=Xnb9RpQJBxZWdnN-DN41Cw1S4HR35FmomKO-ISy476k,4230
nltk/corpus/reader/panlex_lite.py,sha256=QJg00vmQTl-iTot524qmEah5LBiacNA9GmpNp9hhZYE,5440
nltk/corpus/reader/panlex_swadesh.py,sha256=JxpPNLY-WqHZvXsSG7eKFc9eBUHsKO0qOCEUm4iupUw,3287
nltk/corpus/reader/pl196x.py,sha256=e7yXJOjyKh4uTxn78rvSdSF-K8wSDghYutvrf1IMPjk,12320
nltk/corpus/reader/plaintext.py,sha256=o1ekALImW_Uyv4pjXn3y2P7uAevWqAAU-X1FrAinqiA,8456
nltk/corpus/reader/ppattach.py,sha256=vGCcRHcwSMWaHYI1K6KliD9kTKtvabGosqGvPWZjIVs,2903
nltk/corpus/reader/propbank.py,sha256=1Bv1psLPUwLsbfVUuTP9RanC4MoJp1JHboxgRqGJxrs,17776
nltk/corpus/reader/pros_cons.py,sha256=DzQ0xyw7fiyZucbrrOqUooPsDv-SCARTMwTxcVzzwlo,4896
nltk/corpus/reader/reviews.py,sha256=nDNnRfW6XqdJEYh1nKbIOOHFxpHTPD27nxgQanmL8go,12321
nltk/corpus/reader/rte.py,sha256=ihayGJCEZ9Mw4idk337owoXGlC1Ikn4yM_eUZyHlqgY,4785
nltk/corpus/reader/semcor.py,sha256=UxLpm2sbR5_6kPQD-9QSwzYkciA71OGRZ4PGudlln1w,11694
nltk/corpus/reader/senseval.py,sha256=ZNyHHh5gpa2dGQHThMVUoxekDP0DRkrQhKAs8wmY0vI,7539
nltk/corpus/reader/sentiwordnet.py,sha256=yQeTvAT2T1XFg45dgYfFyIJBhXvkxvoEB7H7w-tEosk,4626
nltk/corpus/reader/sinica_treebank.py,sha256=64GeJSNCg3eXUFXDasGIql60OC6Pq1Fwc9OtB8Dsais,2541
nltk/corpus/reader/string_category.py,sha256=-oPUy5R2qb4xiyFsCKGAlUwc3CCsWYCvQsmc2F7NNt8,1919
nltk/corpus/reader/switchboard.py,sha256=yRjPwtDRx-9rZLsBiX_cAcrMl90zaJ2kanD7RB5hT2A,4547
nltk/corpus/reader/tagged.py,sha256=P-gUFkUTazKCJzOlqvwA8aAPWYB6Pw08pkieRduIaJU,12140
nltk/corpus/reader/timit.py,sha256=FpbCiufjEoKPZNgGjvUO1-dAhTq36qeCauaFIzqzRO8,18473
nltk/corpus/reader/toolbox.py,sha256=yVxaqjDOVCjHRNz4Zs_8Zu8fkbi88YEb_JxwCYtHwVE,2121
nltk/corpus/reader/twitter.py,sha256=UdnwB2Hh6quI1KhFAAlriybVrYBoUyNqxzUBCdFLics,4608
nltk/corpus/reader/udhr.py,sha256=tjqXc1JQiTURHnsTU8RIzwxdQIZasbtdMSPz4msnHVo,2592
nltk/corpus/reader/util.py,sha256=fZy5GyMxJ-urpTqO19Sj-oRQqgL-EI65_UDscr2nizg,32225
nltk/corpus/reader/verbnet.py,sha256=NBIwOd2JrnHUamt29yzQjHS5VyzqQsOoCI9ZwOfXZIU,25404
nltk/corpus/reader/wordlist.py,sha256=8dlcXRIjDuJ6U_dvbw6OF_VOgbC4EVeXI8uE2tCxjbM,5812
nltk/corpus/reader/wordnet.py,sha256=nSIDdVHF_FDx6eOlsU5pUgV5z6YjPbhvQoWyEfb8_Yo,93352
nltk/corpus/reader/xmldocs.py,sha256=_fVqoEIAaYKT772IEcYmuk_7OaqUdsRLKZpbaL4up88,16285
nltk/corpus/reader/ycoe.py,sha256=9VbkO_JnFG2joiWfjsfYZ53vsSPl8lWfK00faIAaLN4,10504
nltk/corpus/util.py,sha256=Q9xYJ97UUOy8vuuDA-uidzpE1oEU_-k6M6L0CcxsZ90,5867
nltk/data.py,sha256=rr3iRF4UJi7bh3Ss1Gp0bn8qml3YmTOV8kyWeYOavO8,52814
nltk/decorators.py,sha256=U1-DvExxy0Uv96M0St_rR8IAh8Em3eK6uS4AXIf_Ti4,8526
nltk/downloader.py,sha256=SWUlq_6w6PDWKs4UCXmY3HyvEfDII1Mp7bgjChv-KEM,95506
nltk/draw/__init__.py,sha256=vtk9kECEd_9ZZ0pqST6z5Sb-no-VDpEohi7UHD_YQcE,810
nltk/draw/__pycache__/__init__.cpython-312.pyc,,
nltk/draw/__pycache__/cfg.cpython-312.pyc,,
nltk/draw/__pycache__/dispersion.cpython-312.pyc,,
nltk/draw/__pycache__/table.cpython-312.pyc,,
nltk/draw/__pycache__/tree.cpython-312.pyc,,
nltk/draw/__pycache__/util.cpython-312.pyc,,
nltk/draw/cfg.py,sha256=Y-89bIKWPdiCAn1GkA0eOP08L6eeQHc4gVQKMzBj2sk,30794
nltk/draw/dispersion.py,sha256=MaCehYu6cTuRhMTzDW7E_cwGjXkP7auGCMsD31WjLcE,1854
nltk/draw/table.py,sha256=Gz7IZ6JDxsfLUc5zLui_g1IyTfhPCEJU-u8K71S_qrc,46257
nltk/draw/tree.py,sha256=N8qbNssr6A8OLp4zLE2FJ-jQzWYWFkeASvUeGzc2wKY,39275
nltk/draw/util.py,sha256=8n8YJrrTWSD-MUEy96bX-oaaRBufEtg74bXPXWzWbJ0,90944
nltk/featstruct.py,sha256=BVbotcvgnlNTKMDC1bL16-i3PCw5zXgP7X20tt-yPF0,106108
nltk/grammar.py,sha256=uTC2ScpQIVxWt38QfWceYczkTjTVzPplmD63RfLLKkY,59174
nltk/help.py,sha256=Sj2M3-tktpBZxwHxx1btdthJZ4hhZx-XUXlYuGv2Kp8,1709
nltk/inference/__init__.py,sha256=nw4pQFHOGUv4x7u21GrJBOUS2hc7JibvvgbVnqXuksA,814
nltk/inference/__pycache__/__init__.cpython-312.pyc,,
nltk/inference/__pycache__/api.cpython-312.pyc,,
nltk/inference/__pycache__/discourse.cpython-312.pyc,,
nltk/inference/__pycache__/mace.cpython-312.pyc,,
nltk/inference/__pycache__/nonmonotonic.cpython-312.pyc,,
nltk/inference/__pycache__/prover9.cpython-312.pyc,,
nltk/inference/__pycache__/resolution.cpython-312.pyc,,
nltk/inference/__pycache__/tableau.cpython-312.pyc,,
nltk/inference/api.py,sha256=GdomkZQT97b7Z_HER__KuhCDehRbT1jxD7MoV-1COnY,19560
nltk/inference/discourse.py,sha256=XojyiwkuvpTjBOZCcQ0q5CBpIMOw5fXw3eNzjZPJoqw,22691
nltk/inference/mace.py,sha256=EKRZuwCrX320jzWrq3NOFnfIugapuYdgiH31dVf2ZvE,12243
nltk/inference/nonmonotonic.py,sha256=4_uEiG5h11Cv9_Z7dFIGAZ8M_QpZ-gWNVew11TGbeSU,19174
nltk/inference/prover9.py,sha256=LHyuOmISLo5c64e5VOXiZsK-bG7z-0cb3xY01kWE068,16266
nltk/inference/resolution.py,sha256=wx0YCAC5GgICEOg0VN_x67W88cGHu854aQD3pIJcfq4,26761
nltk/inference/tableau.py,sha256=IpDZT3FbM02R2TnfjmAOlwXivnP19DXoT9Fu6xW4Cv0,26320
nltk/internals.py,sha256=eOGggyeDGf6gyU76p2SU6_vFQP6h5Frg8idDyS6naW8,39416
nltk/jsontags.py,sha256=IWrXxAjSzlgaxqclkKcAnxNzANbG1Zjf_j2jjPnxUy4,1948
nltk/langnames.py,sha256=dDNlEkDmsxWUKxU5CGpYLBHpWlcD1ZrUiO3XTMEPBFU,17957
nltk/lazyimport.py,sha256=qBI5PNKz5qYLrxow19KwLrSE821TyRhVKCafAGML-1E,4719
nltk/lm/__init__.py,sha256=Gsg0OaefWlZVbCDGYqh1cEluhXpWS7vN8Bgpfm3wa-w,8051
nltk/lm/__pycache__/__init__.cpython-312.pyc,,
nltk/lm/__pycache__/api.cpython-312.pyc,,
nltk/lm/__pycache__/counter.cpython-312.pyc,,
nltk/lm/__pycache__/models.cpython-312.pyc,,
nltk/lm/__pycache__/preprocessing.cpython-312.pyc,,
nltk/lm/__pycache__/smoothing.cpython-312.pyc,,
nltk/lm/__pycache__/util.cpython-312.pyc,,
nltk/lm/__pycache__/vocabulary.cpython-312.pyc,,
nltk/lm/api.py,sha256=u65V1dwqoBjCdxNSbLLUpY0zjzY0-WBGK7HHvV0Dct4,8495
nltk/lm/counter.py,sha256=AOqwTQFxaWNMnJZp5E6i5bPBq54Lc7dklp76J5Ty_rY,5250
nltk/lm/models.py,sha256=ricaFU593KT1n8ri5b-3JTxwpO__XXkZCYgPadPmrLA,4903
nltk/lm/preprocessing.py,sha256=yeW6yCp2e0zGFpcQ_puPZ0VBsjcespq2MLPPdUojY3A,1714
nltk/lm/smoothing.py,sha256=GqBAZAgZbQgMFTfu8LJFuWobda4AC8A8va2C3hbkI28,4745
nltk/lm/util.py,sha256=X7x-__sk-f_Z8ttRmLP1ASLIQlVLOVo1ziID3F9qDZQ,474
nltk/lm/vocabulary.py,sha256=rMng32oqXSg1XXOFpRi0TQtjaF_fQNx3b9MGKGakPnQ,7099
nltk/metrics/__init__.py,sha256=gu6faSWxN5vW86Lk7fvzb_NeD4H-5BcUvTqmo5lSNLg,1243
nltk/metrics/__pycache__/__init__.cpython-312.pyc,,
nltk/metrics/__pycache__/agreement.cpython-312.pyc,,
nltk/metrics/__pycache__/aline.cpython-312.pyc,,
nltk/metrics/__pycache__/association.cpython-312.pyc,,
nltk/metrics/__pycache__/confusionmatrix.cpython-312.pyc,,
nltk/metrics/__pycache__/distance.cpython-312.pyc,,
nltk/metrics/__pycache__/paice.cpython-312.pyc,,
nltk/metrics/__pycache__/scores.cpython-312.pyc,,
nltk/metrics/__pycache__/segmentation.cpython-312.pyc,,
nltk/metrics/__pycache__/spearman.cpython-312.pyc,,
nltk/metrics/agreement.py,sha256=NveWTfhBZDQWxqwEWbDMJKpeRElk7vtdN0bh9nMnKWI,16421
nltk/metrics/aline.py,sha256=yWktce0eQ_r60KKUioLZSkorZT8U2zPbxLA-wqLzzDA,32845
nltk/metrics/association.py,sha256=1hNurRIKGop2rDttTKddxtZkMrJ_0qBczugF9Mm4S4o,16569
nltk/metrics/confusionmatrix.py,sha256=szVY58vzNWQRr4objnsiouDcwd-kqAT5_b59wvwx4q8,13035
nltk/metrics/distance.py,sha256=oQ-o9tMyv5kTOJsRe2EKHLlArLiZxlAMJY73tKFnAcw,17661
nltk/metrics/paice.py,sha256=yge6FA2y8_AGYSmCWfzapmKBFF6kkJTe-puWp-KcTBk,14739
nltk/metrics/scores.py,sha256=nv0f5lsR_nKY_A2pJrVMtH4Mzef_CBpDWpkBoHnb-L0,7922
nltk/metrics/segmentation.py,sha256=WdPD6aH31Z55RfEJ7OM0FQoxmGLr_q-q6vEJ0CqxwDY,7221
nltk/metrics/spearman.py,sha256=irorJE5fYsGGxLLvKGvvL6je-hM4aonDBVhWS_ZiOS0,2197
nltk/misc/__init__.py,sha256=pgYpCMn6fRf90Zwn52OjzHDe5MsPgl7u9cbTKeEH8pk,406
nltk/misc/__pycache__/__init__.cpython-312.pyc,,
nltk/misc/__pycache__/babelfish.cpython-312.pyc,,
nltk/misc/__pycache__/chomsky.cpython-312.pyc,,
nltk/misc/__pycache__/minimalset.cpython-312.pyc,,
nltk/misc/__pycache__/sort.cpython-312.pyc,,
nltk/misc/__pycache__/wordfinder.cpython-312.pyc,,
nltk/misc/babelfish.py,sha256=9UkSa6l_j1BHxaT9CU1Viv_51RB0k9AA--3ytmQpsAk,361
nltk/misc/chomsky.py,sha256=UatgZu7Zj3W5DQzuxHZW6Zyxv5kH9l_W2u_ZnOCqXcc,5319
nltk/misc/minimalset.py,sha256=z7-UaqT7F-2ba_qybwY46bgaH4l5zQcyBkg5K_joFQs,2979
nltk/misc/sort.py,sha256=aeqONHRGSDcVksvB6a5npkwdf5sAy6A2vRP9wYp0Y1w,4547
nltk/misc/wordfinder.py,sha256=2HR5Fj4hv7Q2IDFlJb5Nl644uNWtSkWEUe-hBKYt6Zg,4352
nltk/parse/__init__.py,sha256=w2H8yrs8-Wov_wtGTHoMyRyGt7Q4iLyQC6Z05LhuEhc,3797
nltk/parse/__pycache__/__init__.cpython-312.pyc,,
nltk/parse/__pycache__/api.cpython-312.pyc,,
nltk/parse/__pycache__/bllip.cpython-312.pyc,,
nltk/parse/__pycache__/chart.cpython-312.pyc,,
nltk/parse/__pycache__/corenlp.cpython-312.pyc,,
nltk/parse/__pycache__/dependencygraph.cpython-312.pyc,,
nltk/parse/__pycache__/earleychart.cpython-312.pyc,,
nltk/parse/__pycache__/evaluate.cpython-312.pyc,,
nltk/parse/__pycache__/featurechart.cpython-312.pyc,,
nltk/parse/__pycache__/generate.cpython-312.pyc,,
nltk/parse/__pycache__/malt.cpython-312.pyc,,
nltk/parse/__pycache__/nonprojectivedependencyparser.cpython-312.pyc,,
nltk/parse/__pycache__/pchart.cpython-312.pyc,,
nltk/parse/__pycache__/projectivedependencyparser.cpython-312.pyc,,
nltk/parse/__pycache__/recursivedescent.cpython-312.pyc,,
nltk/parse/__pycache__/shiftreduce.cpython-312.pyc,,
nltk/parse/__pycache__/stanford.cpython-312.pyc,,
nltk/parse/__pycache__/transitionparser.cpython-312.pyc,,
nltk/parse/__pycache__/util.cpython-312.pyc,,
nltk/parse/__pycache__/viterbi.cpython-312.pyc,,
nltk/parse/api.py,sha256=R3xG2NEEAq5TvzQpuTKGE_xX_JLoVwp0lTGCQsL2WV8,2354
nltk/parse/bllip.py,sha256=LipdGFlJrKinwKVIgiEKosvdaDrFQGQq-7rZUOkkPNw,10976
nltk/parse/chart.py,sha256=6b3HoLPbT0OlKkETtezIODvqpBKIlkAPoY7r4gV91oM,63760
nltk/parse/corenlp.py,sha256=t9ePlsr3zWxf6CKEbczk71mCVe_y5j9U2sd8el8REms,27745
nltk/parse/dependencygraph.py,sha256=J9DcO7sszs6Pp_TqSRL-pJLwBATw_F98_xaZ9m0-kbM,32468
nltk/parse/earleychart.py,sha256=OVBGyzHbv6M76LyFlHv1Gz-0PvZ0xdRdIYgSb_Qet84,18274
nltk/parse/evaluate.py,sha256=P3qGZ7WG90mOrP4MEQXzDbIOB8WWhDBG3PIDIYfozx0,4468
nltk/parse/featurechart.py,sha256=iK0O0uC-ZAgg6Bcj9ACeQkAvBTrWo-hteu-b-cPdPhU,22532
nltk/parse/generate.py,sha256=kDlYgWj1f_YpbzVJq7OL6O5H0zU03hrrjxLuKLzJgvE,2381
nltk/parse/malt.py,sha256=_XMFcW3SOH0XCrzfv-o4-U8RTkcwDbFSHpfbWOzMtuM,16571
nltk/parse/nonprojectivedependencyparser.py,sha256=ncMpvaMMc8TtRzaPSub8Qt0IUfaXQV42YSbUMQDEnvg,29446
nltk/parse/pchart.py,sha256=KGMYKf5x2psS9XZfU6wbUnIF5jwtoeAtsHzJz6vyZ-E,20480
nltk/parse/projectivedependencyparser.py,sha256=WIWDRYAaUqr8u9UQP5AwSLXbciyoaClSN4TavumkfkY,28243
nltk/parse/recursivedescent.py,sha256=uaVune-fIIWa_wT3CTY4O8p9MhTP4VgW1UoNauPsBZQ,26032
nltk/parse/shiftreduce.py,sha256=Yl2JYRdUOqJIjQXOEWv4fUfpfGu5kLq94HaYPTV5QAk,17071
nltk/parse/stanford.py,sha256=xyPRx710ddMCVYIXje2gTwvZrvYgxOM1CMhM7ziDuVQ,19312
nltk/parse/transitionparser.py,sha256=9EdYOIGZPq77l3fgI4dnH63146YpC5PBfaZKaBPkhlU,32272
nltk/parse/util.py,sha256=6mu9ZVO2hRduKhZGr40bAwRPvhKyTO_Srx2MT8pAy6E,8667
nltk/parse/viterbi.py,sha256=czOjotH__XU_A_Mpf5-xYlYi8bXYOTbIff1mrPZBNYQ,18351
nltk/probability.py,sha256=ikWJyp0Equm4RyGxXOJFBBgNfr2jo2fK0Ck7xwmSks0,92907
nltk/sem/__init__.py,sha256=3-QdBYTgLd1iRHzo6e2f3OT0CDBWZAVmzhnlh7Yvu24,2443
nltk/sem/__pycache__/__init__.cpython-312.pyc,,
nltk/sem/__pycache__/boxer.cpython-312.pyc,,
nltk/sem/__pycache__/chat80.cpython-312.pyc,,
nltk/sem/__pycache__/cooper_storage.cpython-312.pyc,,
nltk/sem/__pycache__/drt.cpython-312.pyc,,
nltk/sem/__pycache__/drt_glue_demo.cpython-312.pyc,,
nltk/sem/__pycache__/evaluate.cpython-312.pyc,,
nltk/sem/__pycache__/glue.cpython-312.pyc,,
nltk/sem/__pycache__/hole.cpython-312.pyc,,
nltk/sem/__pycache__/lfg.cpython-312.pyc,,
nltk/sem/__pycache__/linearlogic.cpython-312.pyc,,
nltk/sem/__pycache__/logic.cpython-312.pyc,,
nltk/sem/__pycache__/relextract.cpython-312.pyc,,
nltk/sem/__pycache__/skolemize.cpython-312.pyc,,
nltk/sem/__pycache__/util.cpython-312.pyc,,
nltk/sem/boxer.py,sha256=rACkFGXGKpK0pMxGXxtpkaKF50-aysHw_9GQjf9xtww,55287
nltk/sem/chat80.py,sha256=TAiZ4BROBzIibz3jVkcgWL_BlmkC-3gMziEySS8JWMw,26519
nltk/sem/cooper_storage.py,sha256=ghg6kYG6a54I4sDiwgzX70Jcm6HlNCPDm5gZi2ZqPG4,4210
nltk/sem/drt.py,sha256=7YDYqc9XuWBleZ0aL41LW_wqpBbpvCMgqMnTOiRXX9s,53149
nltk/sem/drt_glue_demo.py,sha256=xe-SoNG6JVIYtB7YoMO75vEil0KSVGI-gxcfzuLgQdc,19171
nltk/sem/evaluate.py,sha256=07NnlgTmfjJy93UtFfFB6mdZRiW2hrm9ZTfO4RLfjcM,26282
nltk/sem/glue.py,sha256=1nBjlbsqna4gXnNG8iMQn5wb15QPxejyDn-vbTPmXrc,30254
nltk/sem/hole.py,sha256=D9Cnc89WvG9WCDlDNjYF3cig4UmHJgFIwtevuNX1CBs,14216
nltk/sem/lfg.py,sha256=SR-OYvj8HBtIx-EBvnfkOFstSj8eKXzAiVyQeFIsmZI,7716
nltk/sem/linearlogic.py,sha256=Wg_jzVVQDy1nCvYkuJB-g9tdwBYRmsenQboD7eRbEU8,17234
nltk/sem/logic.py,sha256=F0giVBpzZi3HYLQBlsiU0tLOOVEEkswlOYkRsitiVnU,70239
nltk/sem/relextract.py,sha256=UPv_7dKm-GpdYGL_J8CsJHAz0ALxGv_4EZ_rFk7_y7o,15809
nltk/sem/skolemize.py,sha256=CtOfU12APkeIkp-Z_jMlAOMkXIEO9le8TQUvlZ5rBns,5870
nltk/sem/util.py,sha256=GB8SOc7dtyywa4WrlVHr1mHwRntX9ppD7N1em-Bh1Yo,9062
nltk/sentiment/__init__.py,sha256=vre9oZROX6xHjdKjM8inuxYfiVLf341HZ_sPKyoA2Jo,382
nltk/sentiment/__pycache__/__init__.cpython-312.pyc,,
nltk/sentiment/__pycache__/sentiment_analyzer.cpython-312.pyc,,
nltk/sentiment/__pycache__/util.cpython-312.pyc,,
nltk/sentiment/__pycache__/vader.cpython-312.pyc,,
nltk/sentiment/sentiment_analyzer.py,sha256=vB09EsrK-16IqHDD7TdsHZYGrkrUcY1rPzNtHpTrL1w,10432
nltk/sentiment/util.py,sha256=memSkOlHeTfJpeZYtgYJ9LsiMOcoVw4wvwHBOsFwneY,31275
nltk/sentiment/vader.py,sha256=efJHlAUaRr6bV63ZVDIMAOaolVqct6lBKo9tMVCOpkg,21764
nltk/stem/__init__.py,sha256=BUVavJGvw_wgnL3PkklsVg9PNMbz3rCW1U6XTufamC8,1296
nltk/stem/__pycache__/__init__.cpython-312.pyc,,
nltk/stem/__pycache__/api.cpython-312.pyc,,
nltk/stem/__pycache__/arlstem.cpython-312.pyc,,
nltk/stem/__pycache__/arlstem2.cpython-312.pyc,,
nltk/stem/__pycache__/cistem.cpython-312.pyc,,
nltk/stem/__pycache__/isri.cpython-312.pyc,,
nltk/stem/__pycache__/lancaster.cpython-312.pyc,,
nltk/stem/__pycache__/porter.cpython-312.pyc,,
nltk/stem/__pycache__/regexp.cpython-312.pyc,,
nltk/stem/__pycache__/rslp.cpython-312.pyc,,
nltk/stem/__pycache__/snowball.cpython-312.pyc,,
nltk/stem/__pycache__/util.cpython-312.pyc,,
nltk/stem/__pycache__/wordnet.cpython-312.pyc,,
nltk/stem/api.py,sha256=eCDlGturJXqs0AG-q0AHN6EiqU7ytGs43ly-eEiob6s,741
nltk/stem/arlstem.py,sha256=3LW-2dSdsNNAGlQluBiOmHR6V7xOaRdIT2OdlZgcJVk,13006
nltk/stem/arlstem2.py,sha256=2SrhWIANG2Gd9Rxbvj-UIWc7-zr5-zIJGB8HDyszqW0,16535
nltk/stem/cistem.py,sha256=59OmWHS_EWFO-I-ZN1DVFnnz1rdcUxKCBwM4z8DRQoo,7259
nltk/stem/isri.py,sha256=2tOa82AbRnXPympONMJOVc6-CsWdyWlMgcllFwqrJ-I,14990
nltk/stem/lancaster.py,sha256=QrvXzQGULfwIMsW1Z8By8Pz8Exb1MTTydc5ZBV4dJao,12587
nltk/stem/porter.py,sha256=c5jYrt7IHEpV-neeZp6zHOjLXYh83x0tksE3VP7YkRg,28372
nltk/stem/regexp.py,sha256=5lb_FGFd7SixF8hyAZpNNLCCeNDfRhnp750We9dbZJA,1578
nltk/stem/rslp.py,sha256=cMyro3T1eslSdD-sE3Vq5Nnp4yJNrlB6gTQz08tlOxU,5511
nltk/stem/snowball.py,sha256=6somwXR8EoIN9TNmK251dPRGRZ75oC4i6CfPV8iqwk8,183890
nltk/stem/util.py,sha256=ktwGClVb3h-AndTu60wS6rfTAdruI41M1zbccWt7wm0,644
nltk/stem/wordnet.py,sha256=AojhkFURMhpF8vmS7uTkfeJNL-EYrvGb42v-yrTSD8w,1655
nltk/tag/__init__.py,sha256=v7hPbsW3lrb6AFSIZ3uhZ33iwCKVx7RvBHrNkNro1NY,7298
nltk/tag/__pycache__/__init__.cpython-312.pyc,,
nltk/tag/__pycache__/api.cpython-312.pyc,,
nltk/tag/__pycache__/brill.cpython-312.pyc,,
nltk/tag/__pycache__/brill_trainer.cpython-312.pyc,,
nltk/tag/__pycache__/crf.cpython-312.pyc,,
nltk/tag/__pycache__/hmm.cpython-312.pyc,,
nltk/tag/__pycache__/hunpos.cpython-312.pyc,,
nltk/tag/__pycache__/mapping.cpython-312.pyc,,
nltk/tag/__pycache__/perceptron.cpython-312.pyc,,
nltk/tag/__pycache__/senna.cpython-312.pyc,,
nltk/tag/__pycache__/sequential.cpython-312.pyc,,
nltk/tag/__pycache__/stanford.cpython-312.pyc,,
nltk/tag/__pycache__/tnt.cpython-312.pyc,,
nltk/tag/__pycache__/util.cpython-312.pyc,,
nltk/tag/api.py,sha256=hxGeLViDHBmcXYnWtYA8N3r7meYyOzW8nxafEaSXH0c,14810
nltk/tag/brill.py,sha256=PA8cP2tXwbxYtSO33vrV8RwGJNm4ZU_T5iCkqKf0W4g,16829
nltk/tag/brill_trainer.py,sha256=ba0A2b255xtF3N30iiLWy8oazQycJ3IP-gBY6SMuX2w,27900
nltk/tag/crf.py,sha256=eaU05hpUOPRO9TRVt6VXffvCB9ZsahkDu2gQi1x8FFQ,7960
nltk/tag/hmm.py,sha256=mkD06CBebJyBZ-lqXVsyVwCL1blqA0ucijINjynZiJQ,50349
nltk/tag/hunpos.py,sha256=th_TEehZi3QIlaFLJhI40tBpqn1Rn3QYzfv_dT1n1w8,5195
nltk/tag/mapping.py,sha256=TfcRmPsp-cM7FFx03ElLmf0ZYvW48N35SIl_y7M7QHY,4024
nltk/tag/perceptron.py,sha256=ge04T_6s-qIk2ElRLA43euMSzbaxQur2KxosuLfq_Tg,13425
nltk/tag/senna.py,sha256=_Y-mrYv1Y4SH8720pMKiXYvPFnuNJMeDeG6PUu1TyHk,5903
nltk/tag/sequential.py,sha256=hWSEZAYlZa8uKd5-o7NeAIsEaHOj2lZVaVX5F5ymdoI,28621
nltk/tag/stanford.py,sha256=_HuQnKPvcHn01gUtrvhaYI_GpOG2tldthDXoV3daCFA,8427
nltk/tag/tnt.py,sha256=gNsbvbZYFnhPLnbCKCAOdwJIsK7OyNxAje2SfRclyz8,18432
nltk/tag/util.py,sha256=FEApJmJ5lpb1mWbfhRvtKce9sR93WPiRJfUyAvBoc78,2353
nltk/tbl/__init__.py,sha256=7w88VhcTvvCRY03cUftsmoLkf5YanRyM3PXU-Ik2t2c,790
nltk/tbl/__pycache__/__init__.cpython-312.pyc,,
nltk/tbl/__pycache__/api.cpython-312.pyc,,
nltk/tbl/__pycache__/demo.cpython-312.pyc,,
nltk/tbl/__pycache__/erroranalysis.cpython-312.pyc,,
nltk/tbl/__pycache__/feature.cpython-312.pyc,,
nltk/tbl/__pycache__/rule.cpython-312.pyc,,
nltk/tbl/__pycache__/template.cpython-312.pyc,,
nltk/tbl/api.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nltk/tbl/demo.py,sha256=VpqlJQdaXU5wrpJF2aSLhTlF62fOQl5sCYiBJrPZcZU,15338
nltk/tbl/erroranalysis.py,sha256=pav23zp0nOiGydqQ4wSJbXAVJmixxCwgodSthCL-onU,1454
nltk/tbl/feature.py,sha256=CrcNBJ_BGqnEXzFshpz_MGe6mTB1V0rMWBGWyn5Kukw,9690
nltk/tbl/rule.py,sha256=k-0gRVsZOigPWkqn_NuZvOeQ5bXx6YTYuSRA-oUSXPo,11515
nltk/tbl/template.py,sha256=LeTafIw_oehScGgwFgl9fklSIOY5tJvuJNFgzaRsJHU,12892
nltk/test/__init__.py,sha256=79tUwF8keWTdPOWa-gpVx0VkJr6DABwmq9j589IFABU,487
nltk/test/__pycache__/__init__.cpython-312.pyc,,
nltk/test/__pycache__/all.cpython-312.pyc,,
nltk/test/__pycache__/childes_fixt.cpython-312.pyc,,
nltk/test/__pycache__/classify_fixt.cpython-312.pyc,,
nltk/test/__pycache__/conftest.cpython-312.pyc,,
nltk/test/__pycache__/gensim_fixt.cpython-312.pyc,,
nltk/test/__pycache__/gluesemantics_malt_fixt.cpython-312.pyc,,
nltk/test/__pycache__/portuguese_en_fixt.cpython-312.pyc,,
nltk/test/__pycache__/probability_fixt.cpython-312.pyc,,
nltk/test/__pycache__/setup_fixt.cpython-312.pyc,,
nltk/test/all.py,sha256=Ojl5ZxWh7LV7XsdOyR8kWEOxMk7E4m7QnCUrQoACzQk,819
nltk/test/bleu.doctest,sha256=2LmQDiyg-BueBx8LbLJanXCs7UZI9zsLe5nurrXBVBs,862
nltk/test/bnc.doctest,sha256=_8MYpY69ibBaUmkNE3lu187WDOVV_AF4GgTvxSmRuLQ,2051
nltk/test/ccg.doctest,sha256=YHiQsfRdaAuOXrkBfD2WTSd9yWbAWyJ8K2KDtkAWyhk,19786
nltk/test/ccg_semantics.doctest,sha256=vPdQojJQwAxOXJ12cUQXKnHTnU5IOr1z-FszyX1MAAQ,31066
nltk/test/chat80.doctest,sha256=-bY1zKFhVZmtKf82ndwBt3aiNHy7SeSduXjLKggyy2o,8735
nltk/test/childes.doctest,sha256=FcKzuX_RXXHZNhCyU_S2B7xTN46R54mxjYs17bejhN0,9363
nltk/test/childes_fixt.py,sha256=A04g6D3QytqCatGOYAg6fCXqXT8XyjIPbRSkdG1ys8o,372
nltk/test/chunk.doctest,sha256=hDVcWtgJPEZJZLceKbm_885sgpaSR6DFHQMesFzyYeU,11511
nltk/test/classify.doctest,sha256=e1MEsCmHQcLkky6XomMc4je0mhImN8YghEy-A13Ieh8,7699
nltk/test/classify_fixt.py,sha256=zUj2OUNhyi2sJNmVdShbQnuhLUieJ7h_AwKmvj1Gnzw,119
nltk/test/collections.doctest,sha256=ygTv1l_HbXdVHDw91rjUxJy6KFnSvbvzpkXLvTfnVUU,622
nltk/test/collocations.doctest,sha256=ezSwbUboIG91fbmKX7mvFo3c9C_BlbgM85tRrctZAKU,12506
nltk/test/concordance.doctest,sha256=rPESoW6-ugpgj2LgBYgChcjOWv75x0eu3Y4A2MuZSsQ,3544
nltk/test/conftest.py,sha256=apMfe_V5EHXZHCXCMxVqMt3gpHCUwwxR-MxESis9--Q,804
nltk/test/corpus.doctest,sha256=K3vInYJLjonuHTysZCrHvZ1ytgseREpqZMcz9VsolHA,99206
nltk/test/crubadan.doctest,sha256=qn63dlNT7XmxUA1m2JmcwuX_i12_cWCAfJDVxSp9Kgs,2060
nltk/test/data.doctest,sha256=ggmXuZT1Hy7Bd5_bppQPhz64lO8E-dDHXtuh2hQ5HYM,14266
nltk/test/dependency.doctest,sha256=pXyaAjDE232xA0Q2EEvxkMNu8COSk0l52ELMi07C-qI,7669
nltk/test/discourse.doctest,sha256=AxJetWi0DetMfptecrI9lXac7mN4YVrfCVPr3VXDBkQ,17923
nltk/test/drt.doctest,sha256=wTRxo8ISpwcsF9RpwlHn0PLfpaL5ZCZxN2oEpqlGHYg,20076
nltk/test/featgram.doctest,sha256=APe6OaMrt1UwuKzLM-7NRXIQF0mPnpZj4vR1yTSnz0o,28870
nltk/test/featstruct.doctest,sha256=hjCDZs3NN2DJdyAH9BygfXUuWAP723xQFk3YMSg7p_Q,38894
nltk/test/framenet.doctest,sha256=KcZIkt-8_HJ2gDl7BvWG-vfOI3NI7Be2VT9sc9hcAXY,10797
nltk/test/generate.doctest,sha256=7yYAJ1EO1qAHy9OYVcoEehPs4hoEhvTvs0VH5H08oBg,2050
nltk/test/gensim.doctest,sha256=kmwbF1XcgZvyfrfdotjNw-VlkzgGFVxwVObQ0IPcAXI,5200
nltk/test/gensim_fixt.py,sha256=2p-4RObBWBU-YzbGPXAj03aPhimK-l_RLNdS_RuKHh4,77
nltk/test/gluesemantics.doctest,sha256=QVR_Hki10s1CRghGvxJDNDA2u1J89uj1O_nKt0RF8eo,12705
nltk/test/gluesemantics_malt.doctest,sha256=4S5usX2BTPAPpK7t1SIfwHGaajpVXAuk5CuAUvSagZU,2667
nltk/test/gluesemantics_malt_fixt.py,sha256=H7YUsT_M5LteTBF9utPjqUP8pybUaW3w2fQdGUPiT3c,232
nltk/test/grammar.doctest,sha256=z2ZzpqBjN64pPB7J3WbZcI_QsH5a1TkgdNKN5GsHMLE,1949
nltk/test/grammartestsuites.doctest,sha256=4eF9lME7iQqaAUMvp3IX0s815yi9MSUgbDrT_hJov2c,3309
nltk/test/index.doctest,sha256=MuVP-xRyC9Zu_bY7PU8BZekRztycmrD28ngq6q6RonI,2701
nltk/test/inference.doctest,sha256=8dABBDz575EQIw1qMwVdDCEQClRmaRxxMARqC1CHYBw,18365
nltk/test/internals.doctest,sha256=dzXAofPbEfck_BZYO1ZHp5NpqQd_Ofz3nGKjP_KlzHE,4283
nltk/test/japanese.doctest,sha256=79O_D43s3rVCS0Am06pG2hCiNUkT2A7AT1Up_BT82_g,1093
nltk/test/lm.doctest,sha256=mTo97a-_5wZ1t_G3Hso-LuhluTTtHZrsE5b89dhQXfY,3951
nltk/test/logic.doctest,sha256=h8numvKfRWQucoEA9HPJ02pxviSb8SdCa02BUvWPL5o,35183
nltk/test/meteor.doctest,sha256=dcxi8dfWOG6fm-7_VlHkxnaW8d3vEw6TBir0iAsf2Qo,1523
nltk/test/metrics.doctest,sha256=h9P-WsPkMHmFQDQw98TRwfRBAVhkuLqE3MOSl3hZaJY,11283
nltk/test/misc.doctest,sha256=upseLcrsXziqxxF0pCmA1Nyx9ovGAuUlpG7w4PK8a1k,3464
nltk/test/nonmonotonic.doctest,sha256=mG0_JgtIhZh9pD-jqiGAHD7nuQHGClxWhKvffiBPHNM,10370
nltk/test/paice.doctest,sha256=9KOoUsd6O-ACSMbt3Ras4zS-FE55R4jZ6xh1JKopb3c,1273
nltk/test/parse.doctest,sha256=fKDMs2TD3qhUgNdwW30CTYcEOl1olf3TRCHsf_i1heY,34936
nltk/test/portuguese_en.doctest,sha256=voMsH9rhsaI2ETsXYI8T8-ZXKYv6l1-ghv4TMaEDt7c,23121
nltk/test/portuguese_en_fixt.py,sha256=-66oHXFBbvDKHkwkcOLnpwcn29iUpwUEWrJ-LqSk5FM,130
nltk/test/probability.doctest,sha256=4BuJPzdy6l8hlRXIJOEMxfzlLl1oaru2ezTV6olNx0U,9244
nltk/test/probability_fixt.py,sha256=avszs9PHMTVYHHcOXA19-EsTYDahH4VPPPMqv1QkGpE,188
nltk/test/propbank.doctest,sha256=jDD0XEFjm2-hDA8g_Y7Us3feNLeOgsw8lvDn-tApO0g,6694
nltk/test/relextract.doctest,sha256=Klf15poDywGJTJlpmpTBnLF5ur-_0tLZC-S93Sox95A,9520
nltk/test/resolution.doctest,sha256=XJw6Bs4CBYSEEWreyqWTaoTo1ADGwGwRptEsZV8qUH8,8010
nltk/test/semantics.doctest,sha256=XhmM0qSpAcmqYZu6dV7Veugf3FpuyMxaHEh1t2UEqwI,25190
nltk/test/sentiment.doctest,sha256=dwARYfcbIn6oaPX7kRAo_ZjjJ_YDowxh3zAgr-16Mak,12229
nltk/test/sentiwordnet.doctest,sha256=7wIk6gIiYtONvkpNfAUK_xk-jXNVVzIzPlQJ4h2UTrk,1051
nltk/test/setup_fixt.py,sha256=IQUyYM-mNaVbfsGFvfOdJc0ymAJ-0u5OAZd2cqRgF0s,912
nltk/test/simple.doctest,sha256=ZF_0SZ5vp7pMfFp6iKf3ZvKkRYLHlxBamC0aaQItSog,2407
nltk/test/stem.doctest,sha256=XJu6ADeinzu41KgldR5pVuiLdzkmCsoJIXLaSQocTPs,2552
nltk/test/tag.doctest,sha256=Dl3QKGZi-1uJQwQKenGThEIfOLKKR7k3wghJKAf4GC4,34100
nltk/test/tokenize.doctest,sha256=9fZOgyZnwOBNiPYS0xRFSPcr8asz18Tc29Et8_nzHs4,20353
nltk/test/toolbox.doctest,sha256=NfbQ7Q_WFajCTtjxcLSzYkkvr8VVm8SGtqDerv5KBJ4,10323
nltk/test/translate.doctest,sha256=if9_vzqjIWk0wnok6QSaLO-dry5lt3DLWCTj99VWmf0,8396
nltk/test/tree.doctest,sha256=_MYclk55SLXJ4zGRu-bbL-oPOThpWc2G4c0cuLkyXXo,47273
nltk/test/treeprettyprinter.doctest,sha256=yuAL_WWYVV5jCAe0TmzQ9j4N2CSZD9r13_g992DUvkM,9376
nltk/test/treetransforms.doctest,sha256=UDSeLha6tfB-PN4_eJGQeOMifVhTIY89tho_2cuXDyc,5006
nltk/test/unit/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nltk/test/unit/__pycache__/__init__.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_aline.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_bllip.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_brill.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_cfd_mutation.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_cfg2chomsky.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_chunk.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_classify.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_collocations.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_concordance.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_corenlp.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_corpora.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_corpus_views.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_data.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_disagreement.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_distance.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_downloader.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_freqdist.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_hmm.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_json2csv_corpus.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_json_serialization.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_metrics.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_naivebayes.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_nombank.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_pl196x.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_pos_tag.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_ribes.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_rte_classify.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_seekable_unicode_stream_reader.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_senna.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_stem.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_tag.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_tgrep.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_tokenize.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_twitter_auth.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_util.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_wordnet.cpython-312.pyc,,
nltk/test/unit/lm/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nltk/test/unit/lm/__pycache__/__init__.cpython-312.pyc,,
nltk/test/unit/lm/__pycache__/test_counter.cpython-312.pyc,,
nltk/test/unit/lm/__pycache__/test_models.cpython-312.pyc,,
nltk/test/unit/lm/__pycache__/test_preprocessing.cpython-312.pyc,,
nltk/test/unit/lm/__pycache__/test_vocabulary.cpython-312.pyc,,
nltk/test/unit/lm/test_counter.py,sha256=t1Dgd6b6JFrtkggAzoRXSGfZ1gwNKiD4aM_cBPoFsEM,3891
nltk/test/unit/lm/test_models.py,sha256=3yVFNUsVC2_VUJexqclnSjInpQe14MuPVq3qsYlr2lc,20160
nltk/test/unit/lm/test_preprocessing.py,sha256=05lMhOdkglGipiMuJ0gTJwaGweYed0qVGade6VY1uXw,999
nltk/test/unit/lm/test_vocabulary.py,sha256=1Pd5rfniE0C_Pq5v2k3IKGdug6ZuA4ycbNZKfRpCfuI,5917
nltk/test/unit/test_aline.py,sha256=u9y3D19sJGY_VE9nSOOON5GMYyTIRGVXMyqveeMypyU,1130
nltk/test/unit/test_bllip.py,sha256=rm3KrL9F6F49WfXDV42GpuPlvbvsXibTbUXU0pWa-pw,1115
nltk/test/unit/test_brill.py,sha256=ZTDCN3y2mTZ-dLeL0GwvO3yM6LlcTmIQ1LaNMBtXZyE,1024
nltk/test/unit/test_cfd_mutation.py,sha256=YutOmFGBizpUzZ61l1cDxIegJah2ntuwKL88C49-WBA,1373
nltk/test/unit/test_cfg2chomsky.py,sha256=g2wKKjmogJpAaRiLxA0_xw4KLwZa4zvjA-gBMt9z1l0,1726
nltk/test/unit/test_chunk.py,sha256=xo-ItBtJdBsRIt-rX1rYYkV_ufABgPK4e6HlogRTvWg,2219
nltk/test/unit/test_classify.py,sha256=4Bv5-rDyrjDGdmRHY_qh4Rq_5VdoghzlJEPB_PCIzQo,1337
nltk/test/unit/test_collocations.py,sha256=vaiBeImr5dCDOhFPAN8Q4CAUVojJTwNHpodolyIymCU,3690
nltk/test/unit/test_concordance.py,sha256=91x9LT-875n7OOi3it5O1qr1xKdOA1ufZY3KLH10Iaw,4108
nltk/test/unit/test_corenlp.py,sha256=doMoc3Drnl3fDFxTaZKVPKq-75RTXOGSOqkI9K_74FQ,58632
nltk/test/unit/test_corpora.py,sha256=46IA2v_oxDRFQQGa6iGTHiTHAPsDZjIovDSG2432Ubc,9923
nltk/test/unit/test_corpus_views.py,sha256=mIxoCvqWSfInEQkISPwfZvTG6dTxYh7Bx0kCGC6VsoA,1600
nltk/test/unit/test_data.py,sha256=y1fXWnIylRrff9fBJBUYZ6xw3T6uwMg_6View-jKcas,390
nltk/test/unit/test_disagreement.py,sha256=e2JIXrNqCg1YTSh6P2lnGs9YN8KmkWcFD-zcZPsNkjk,4461
nltk/test/unit/test_distance.py,sha256=DIMhkfn2y6WvsiJRyw1y_T5b_4OHI6wG01eEAt8Cd9Q,5839
nltk/test/unit/test_downloader.py,sha256=QvpnRVehOfLZVJ-iUH8m5mEHG8w4deKxRhF7IOnjAZM,741
nltk/test/unit/test_freqdist.py,sha256=I6qkc8zleTMeivGWB0NntBVQDx_tVxthWRwcOB-T0i4,210
nltk/test/unit/test_hmm.py,sha256=bX7fSFd7k89JCr9VNFr1ZAng4m2KkfuTL_M2TNvA1nU,2285
nltk/test/unit/test_json2csv_corpus.py,sha256=-BUZfzFHAof4umKGAL-9WKGUBiPFyKzLVZOCmzW-a-g,5888
nltk/test/unit/test_json_serialization.py,sha256=CfpHkTvY0lF8rMQXQsv_0nSVhDfhxVkqDwTLq26pv5Q,3634
nltk/test/unit/test_metrics.py,sha256=iK6bLxVi1fVll-2eCmgzE-ubWnQlFeQjP079qdiRP-A,1949
nltk/test/unit/test_naivebayes.py,sha256=a_tjsQsyvPIsO3mrtmN6knaC9BFwPE7PDNHBSNdhYMc,764
nltk/test/unit/test_nombank.py,sha256=gIgs6vlEI2NheAh8c6wlJdk6apHmAMmaDZkP8laIvKY,760
nltk/test/unit/test_pl196x.py,sha256=C41qhbllNBqtVJ9tCFM8mReQqzsdbM7uoMo9hFVHKLg,410
nltk/test/unit/test_pos_tag.py,sha256=5HkW7hpjZd2270RVSFXECLxXg8jCY2iBViDoDA8O2Qs,2782
nltk/test/unit/test_ribes.py,sha256=DItkydO5d543kRFYiAebnqudiF2HETHrMAntG3H75jA,5204
nltk/test/unit/test_rte_classify.py,sha256=oNGw78oedct_VpwelsMVFb7v3bRFepnQWWbHgKp3GBQ,2765
nltk/test/unit/test_seekable_unicode_stream_reader.py,sha256=XBxkic2HcfqxfTY0XxBBBRNEo5FQrYYQzkg1vywwUA0,2265
nltk/test/unit/test_senna.py,sha256=fuLdpQO7kG-12rWpGprIOiH9fwxhv1yseNxKtpcUmss,3712
nltk/test/unit/test_stem.py,sha256=kjtoZlKkgtCZYX8kxyVQIPf5f6QSPzUCLkCJLDvDWFA,6347
nltk/test/unit/test_tag.py,sha256=h7YztNxvYcx2177MkQrPqPgYR2gL1sdl9YB3ZMKuciw,535
nltk/test/unit/test_tgrep.py,sha256=elx0roGwZJEOJy7-j7cqwAXvzvldlzYkxo8lHDoKf8E,31708
nltk/test/unit/test_tokenize.py,sha256=9uQx21Vs5Iv5mBmNyiHqTaOmIecSlD1n9jUY9dF1mBM,30921
nltk/test/unit/test_twitter_auth.py,sha256=bms9DQ07DwEr53IqMr49qGL9ria_1rEf3aA7xt8oR-A,2509
nltk/test/unit/test_util.py,sha256=UMUTzBJRSSAdFwp7tZkG7gygQ9gHcrk2IEiuq6XvTRA,1888
nltk/test/unit/test_wordnet.py,sha256=tZCn_lZVJ8POuehMbAIcgV000vCMwXFJUbdhuPEOSmw,9260
nltk/test/unit/translate/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nltk/test/unit/translate/__pycache__/__init__.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_bleu.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_gdfa.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_ibm1.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_ibm2.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_ibm3.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_ibm4.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_ibm5.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_ibm_model.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_meteor.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_nist.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_stack_decoder.cpython-312.pyc,,
nltk/test/unit/translate/test_bleu.py,sha256=YpYHqzzebFqF8M-x2AjUYeI3YNmRSseJmAf4xoT13XY,15874
nltk/test/unit/translate/test_gdfa.py,sha256=E4r6o0S2r5rzK7NPFTyDA2a7IAfYzmIhCU4WXB0Wvdo,4770
nltk/test/unit/translate/test_ibm1.py,sha256=7dGtPK_T9qXGTmB8skOeJ_mNJ2G8VaoYOlqMkhP0fBs,2669
nltk/test/unit/translate/test_ibm2.py,sha256=fAjggyyMHRzPmpuFdov7dWfwsNM7hK9Z8p_qalCn_lY,3377
nltk/test/unit/translate/test_ibm3.py,sha256=9PUQNN75ITw_TgzWayxQrMZRNS6pdyNThSw-sTdscP4,4189
nltk/test/unit/translate/test_ibm4.py,sha256=r1gfJCmXlP0UZhZdrdzc0CcJLZNCn0zrn_BIMH0dVDk,5209
nltk/test/unit/translate/test_ibm5.py,sha256=20agaTpArhfMcx-Ady0BUXyxayBU_ipPiDTvb8s_1oo,6761
nltk/test/unit/translate/test_ibm_model.py,sha256=qTMFR4acSkEP5-kta-9B6RymoswEIitV3ljn86riNCo,9676
nltk/test/unit/translate/test_meteor.py,sha256=sldeMjDkStoMnxBnx1MKDRNBGmcs4Hdu9VmMSzpl1Jo,750
nltk/test/unit/translate/test_nist.py,sha256=HFfcs5Gq_goyYm-NSqdb_Eet6kClibKNvcr3gdasMmk,1645
nltk/test/unit/translate/test_stack_decoder.py,sha256=37pm9cVUc-I0789Yt-yUZME9wG6Xrcdzqs0a3lB_8mg,10000
nltk/test/util.doctest,sha256=BYtTUbvvvKXlM57NfVs-QGe2YqSN3M_Ad40fJNI0go0,1058
nltk/test/wordnet.doctest,sha256=5VI2tl-FxJ-NCwpMbv5AMvy8vvEoqaLW1hUNu2mzV9A,30528
nltk/test/wordnet_lch.doctest,sha256=5a80en1DUmUKj9RepGbrpJmPsgvYSFCezlYqh_da9ME,2361
nltk/test/wsd.doctest,sha256=wa0eAdE0glaOrzkY7VnxIb297tmA46TsuNqdeyHUNR4,3014
nltk/text.py,sha256=TVmS9X9weLrodN2y_VlHqSYaJi6LOh9U6lpLtyAJU0o,28909
nltk/tgrep.py,sha256=g3BNjLGCcIrKUnwXRoMd32f-l-8JqF6mUrJfxFKLOUI,37911
nltk/tokenize/__init__.py,sha256=0qmUpIe6PExgEzV9lXnzeiSfGkHVLoIxVrcZIgBF3FA,5243
nltk/tokenize/__pycache__/__init__.cpython-312.pyc,,
nltk/tokenize/__pycache__/api.cpython-312.pyc,,
nltk/tokenize/__pycache__/casual.cpython-312.pyc,,
nltk/tokenize/__pycache__/destructive.cpython-312.pyc,,
nltk/tokenize/__pycache__/legality_principle.cpython-312.pyc,,
nltk/tokenize/__pycache__/mwe.cpython-312.pyc,,
nltk/tokenize/__pycache__/nist.cpython-312.pyc,,
nltk/tokenize/__pycache__/punkt.cpython-312.pyc,,
nltk/tokenize/__pycache__/regexp.cpython-312.pyc,,
nltk/tokenize/__pycache__/repp.cpython-312.pyc,,
nltk/tokenize/__pycache__/sexpr.cpython-312.pyc,,
nltk/tokenize/__pycache__/simple.cpython-312.pyc,,
nltk/tokenize/__pycache__/sonority_sequencing.cpython-312.pyc,,
nltk/tokenize/__pycache__/stanford.cpython-312.pyc,,
nltk/tokenize/__pycache__/stanford_segmenter.cpython-312.pyc,,
nltk/tokenize/__pycache__/texttiling.cpython-312.pyc,,
nltk/tokenize/__pycache__/toktok.cpython-312.pyc,,
nltk/tokenize/__pycache__/treebank.cpython-312.pyc,,
nltk/tokenize/__pycache__/util.cpython-312.pyc,,
nltk/tokenize/api.py,sha256=gSsubjy4wvCeoLz2LIToXV7fEbH-ZYuU1ZQKiZAUATc,2357
nltk/tokenize/casual.py,sha256=38kW21jjDImAPLoCsvoCIHV91U6SqKYKroIzts82f3o,16101
nltk/tokenize/destructive.py,sha256=JkyvgJ4vbKQ7PKeG5_Ss3LWvYVhCLzrnuNC9mmXO66U,9447
nltk/tokenize/legality_principle.py,sha256=AIbUBCKtuAvehwOkC0Aa4lGmc8vJoRrNN5hm7wCpfyg,6236
nltk/tokenize/mwe.py,sha256=lKHLQ-4lwHuhDeg3OJvTnq1xPk2H-Sp78VYL1WRovQ0,4181
nltk/tokenize/nist.py,sha256=-EXf8gKQOFQmuymmIsslr1RInp4lS20rgXlECRNWbdA,7720
nltk/tokenize/punkt.py,sha256=KVKagPpsPNYKzGc6a9sPkBMUlHjw5LwZxWr_r4z6hpw,68804
nltk/tokenize/regexp.py,sha256=4oFnXCBYYwHbl9PLVIRdLIsMoFDKvhUvXUTB0N_Yu10,8331
nltk/tokenize/repp.py,sha256=0c9syu4dcvFjlFt-bwIIlM2uunhtnAc0rcR_QqfjYJ4,8245
nltk/tokenize/sexpr.py,sha256=NZazV0MD6VJH30gHaf4Ul4NlJMZ5r5S9ZR8xOROnezw,5302
nltk/tokenize/simple.py,sha256=Ie7Fhs95ubIHTzb-1Kfnfm0xjRNNsR9Jplu9Ei8v728,5379
nltk/tokenize/sonority_sequencing.py,sha256=XBiVVAp9f2kCX_bg80r0QI835uF0jtTMUuCUimsqWPQ,7739
nltk/tokenize/stanford.py,sha256=_p90fpkx6hmIwBtZYsKsXRa0i0OthUZ2kJqgXAkHLRY,3875
nltk/tokenize/stanford_segmenter.py,sha256=E3Y1HS4Y298DpKFvotlpWf2YMcO7zCs7JrajhugJf1Q,9857
nltk/tokenize/texttiling.py,sha256=QYDETnGqm4RdimXBZNInhALjzt0Wca8M3mH8aaX1lFU,16943
nltk/tokenize/toktok.py,sha256=R1eW8VozEtxuwDYjNVn2eWSGSTDvtTKtASMhDvdrdIk,7679
nltk/tokenize/treebank.py,sha256=ahB5jsQrFeXS-H1Y2Cm5dkkJE91Dva5nWI9Io-Uco2M,16669
nltk/tokenize/util.py,sha256=iw9hPFtD_oZOVetjcCjh0P6s98u_fjbMDgpxGxn4RGY,10339
nltk/toolbox.py,sha256=bMXsrkHbgGrP-Ktg2MBLDr-6MJupIlkTRtBhLUwI7tY,18337
nltk/translate/__init__.py,sha256=a9SOGnQ057m96m5YY8JOP5OIWi353ManWall-EIlxCo,1331
nltk/translate/__pycache__/__init__.cpython-312.pyc,,
nltk/translate/__pycache__/api.cpython-312.pyc,,
nltk/translate/__pycache__/bleu_score.cpython-312.pyc,,
nltk/translate/__pycache__/chrf_score.cpython-312.pyc,,
nltk/translate/__pycache__/gale_church.cpython-312.pyc,,
nltk/translate/__pycache__/gdfa.cpython-312.pyc,,
nltk/translate/__pycache__/gleu_score.cpython-312.pyc,,
nltk/translate/__pycache__/ibm1.cpython-312.pyc,,
nltk/translate/__pycache__/ibm2.cpython-312.pyc,,
nltk/translate/__pycache__/ibm3.cpython-312.pyc,,
nltk/translate/__pycache__/ibm4.cpython-312.pyc,,
nltk/translate/__pycache__/ibm5.cpython-312.pyc,,
nltk/translate/__pycache__/ibm_model.cpython-312.pyc,,
nltk/translate/__pycache__/meteor_score.cpython-312.pyc,,
nltk/translate/__pycache__/metrics.cpython-312.pyc,,
nltk/translate/__pycache__/nist_score.cpython-312.pyc,,
nltk/translate/__pycache__/phrase_based.cpython-312.pyc,,
nltk/translate/__pycache__/ribes_score.cpython-312.pyc,,
nltk/translate/__pycache__/stack_decoder.cpython-312.pyc,,
nltk/translate/api.py,sha256=SM3sIpzqhMYSFxnOHo1G30y9hkxKrQtE2Ugi1Qln03o,11109
nltk/translate/bleu_score.py,sha256=YpFNn80ydIWg8iKHgAJ7jgFFkPrkuEMku6m1rC9X_kA,30415
nltk/translate/chrf_score.py,sha256=C7mEHCk0Jn7QdmxctQImEja-HovmvpPOCfos_UhUH80,8978
nltk/translate/gale_church.py,sha256=fy4jIbJpZmiJyjVEc_2s1ng6BORp63vG9_HbEToFM6E,8732
nltk/translate/gdfa.py,sha256=dMWFOM72FZh8d3iuJhyhQZ8KxMfbusSzR0Nr74lyfKQ,6246
nltk/translate/gleu_score.py,sha256=symY8I6w4SxQDKUN9EyJL6_1Da-4bVT_y-Kd2nnFe38,8831
nltk/translate/ibm1.py,sha256=MnR2l9vpkyxCAyqGd6ajMUkVxsp5Q6m8Hz--lLzWVMs,9522
nltk/translate/ibm2.py,sha256=kaGLTJfIrsy3KYj1-72vFYMNtDD8ptJst73mS4NH2nk,12561
nltk/translate/ibm3.py,sha256=E9aNT2lKkV0I9wwGCvhZPWXk7FUl5VwqV2qPeDUbTck,14154
nltk/translate/ibm4.py,sha256=1kUdywKq3klNtmId3AHrdYhk-3ptzYlaRw9CH7Jydzk,20765
nltk/translate/ibm5.py,sha256=H6V--iB46jm9btUyvRSaJ09L2fPCda8xNhbNW9AbtKQ,27957
nltk/translate/ibm_model.py,sha256=AXy2cgctd8CBV77lih-Yvw2G32xyCO8YMLIGpTugXHU,20504
nltk/translate/meteor_score.py,sha256=-ZKuCuXxmexx3U-GkpUBvbxNyFEyKZUL16ZIkNnOVYY,17301
nltk/translate/metrics.py,sha256=qyM4DXkdyRY6OYiHR0M9anTGl129lsHyKIzutfJ-Low,1513
nltk/translate/nist_score.py,sha256=5n8KyFK_99PcGPGE_l-wkKPbbj9Uy68iE4MZiWEKaxY,8148
nltk/translate/phrase_based.py,sha256=KBfNqROhEiut1N0C-nFDCivllejA4OUeZ6BLyuMNYTA,7860
nltk/translate/ribes_score.py,sha256=OGPOh-byCf0R7i2aQYWR-Vh21RJtl9nxQKHEH_YuBto,14027
nltk/translate/stack_decoder.py,sha256=MuRPezJG4gq2TQsArF0Cm3xn2X6E8FAKSg9BD4Qx4cI,20516
nltk/tree/__init__.py,sha256=mruLTDldjRtc3viZeVxuUp_QaWW49aRrCSIlFUgln9I,1466
nltk/tree/__pycache__/__init__.cpython-312.pyc,,
nltk/tree/__pycache__/immutable.cpython-312.pyc,,
nltk/tree/__pycache__/parented.cpython-312.pyc,,
nltk/tree/__pycache__/parsing.cpython-312.pyc,,
nltk/tree/__pycache__/prettyprinter.cpython-312.pyc,,
nltk/tree/__pycache__/probabilistic.cpython-312.pyc,,
nltk/tree/__pycache__/transforms.cpython-312.pyc,,
nltk/tree/__pycache__/tree.cpython-312.pyc,,
nltk/tree/immutable.py,sha256=NOmT_xXNUrk3ct0m7ZMNhqj_kL-91lRdP_ZwTc6bZEc,4178
nltk/tree/parented.py,sha256=0VrrC0i7eBQuj0Q3H4bGrhWXXzlqvIX-7efnZmXtNTI,23192
nltk/tree/parsing.py,sha256=IpYHYTBD_VZkp4m0NbC8qmhj9ZiQVI_8x4iJmoe2Hp4,2083
nltk/tree/prettyprinter.py,sha256=1nybXp3GGP8AF_FxG_v6xrXiQxyvTAt7yseLLZ0QLdI,25586
nltk/tree/probabilistic.py,sha256=mOnOfXKwE_OeIs4llAStM0vtm4DOqDbWgMOrR42Ni6U,2492
nltk/tree/transforms.py,sha256=PmprKuO_0pYsSxQPdhnNnJsRmZNOtDYkImwZMoOVfvY,13689
nltk/tree/tree.py,sha256=euTPBiCu9qVe2_-amu74Ew4SdqxrfIC0itRZkh_r05Q,36500
nltk/treeprettyprinter.py,sha256=TJU6UHvemo2C_9a0LdTYS6n4ES4QjVf4sQcXq8HapQo,975
nltk/treetransforms.py,sha256=B_0-bNh4gkTu2dY8E5mt8XQdN97IKFL7LrtOMeASRps,5288
nltk/twitter/__init__.py,sha256=npOhJzWN63BFf7aPDfUAia76r8uVgVy3ll6ohLKS8fU,819
nltk/twitter/__pycache__/__init__.cpython-312.pyc,,
nltk/twitter/__pycache__/api.cpython-312.pyc,,
nltk/twitter/__pycache__/common.cpython-312.pyc,,
nltk/twitter/__pycache__/twitter_demo.cpython-312.pyc,,
nltk/twitter/__pycache__/twitterclient.cpython-312.pyc,,
nltk/twitter/__pycache__/util.cpython-312.pyc,,
nltk/twitter/api.py,sha256=yAHSw0JeVRAKJOHvWGqml3U3t7GYaQwhPU3Kf1sRYqw,4692
nltk/twitter/common.py,sha256=RJbcw3Wvr6YTnQ4YqfWQDN494vKATE_REx-5-9YL5Qg,10120
nltk/twitter/twitter_demo.py,sha256=dCplglFGLm9I5NgSt3UHVM-NVES2zWWjDnVPRFUONIQ,8309
nltk/twitter/twitterclient.py,sha256=Vliyz7To9z2IJmUUg6AWcwDS-gNIIF2Yi5fuPfJN0zE,19927
nltk/twitter/util.py,sha256=Ij7TX3ypt1uYcfo6nSwCbygUpK6_59OhNN3l1ZPefS0,4546
nltk/util.py,sha256=VijbYZYpM5wjWEsgI0uu-GnR-zJMulfBoDgeJI9QpGw,42026
nltk/wsd.py,sha256=5Ie3V_RtWQGRsIeL0rMnGGA_9KEGD_9l9GkbkFyYQoA,1789
