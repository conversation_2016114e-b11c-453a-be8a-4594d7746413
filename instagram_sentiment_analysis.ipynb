{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Instagram Reviews Sentiment Analysis with NusaX\n", "\n", "This notebook scrapes Google Play Store reviews for Instagram and performs sentiment analysis using NusaX models for Indonesian, Javanese, and Sundanese languages.\n", "\n", "## Features\n", "- Scrapes 2000 Instagram app reviews from Indonesian Play Store\n", "- Multi-language sentiment analysis (Indonesian, Javanese, Sundanese)\n", "- Mixed-code text processing\n", "- Interactive visualizations\n", "- Export results to CSV"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages (run this cell first if packages are not installed)\n", "!pip install google-play-scraper pandas transformers torch matplot<PERSON>b seaborn tqdm langdetect requests beautifulsoup4"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "from google_play_scraper import app, reviews, Sort\n", "import time\n", "import json\n", "from datetime import datetime\n", "import os\n", "from typing import List, Dict, Tuple\n", "import re\n", "from langdetect import detect, LangDetectError\n", "from transformers import AutoTokenizer, AutoModelForSequenceClassification, pipeline\n", "import torch\n", "from tqdm.notebook import tqdm\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set style for better plots\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"✅ All libraries imported successfully!\")\n", "print(f\"📱 Torch version: {torch.__version__}\")\n", "print(f\"🔥 CUDA available: {torch.cuda.is_available()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration settings\n", "CONFIG = {\n", "    'app_id': 'com.instagram.android',\n", "    'country': 'id',  # Indonesia\n", "    'lang': 'id',     # Indonesian\n", "    'review_count': 2000,\n", "    'sort_order': Sort.NEWEST,\n", "    'batch_size': 200,\n", "    'delay_between_batches': 1  # seconds\n", "}\n", "\n", "print(\"📋 Configuration:\")\n", "for key, value in CONFIG.items():\n", "    print(f\"   {key}: {value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. NusaX Language Detection and Text Preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class NusaXLanguageDetector:\n", "    \"\"\"Language detector for Indonesian, Javanese, and Sundanese.\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.language_patterns = {\n", "            'indonesian': {\n", "                'words': ['yang', 'dan', 'ini', 'itu', 'dengan', 'untuk', 'dari', 'pada', 'dalam', 'tidak', 'adalah', 'akan', 'sudah', 'bisa', 'juga'],\n", "                'patterns': [r'\\byang\\b', r'\\bdan\\b', r'\\btidak\\b', r'\\badalah\\b']\n", "            },\n", "            'javanese': {\n", "                'words': ['lan', 'karo', 'iki', 'kuwi', 'saka', 'kanggo', 'ing', 'ora', 'iku', 'wis', 'iso', 'uga'],\n", "                'patterns': [r'\\blan\\b', r'\\bkaro\\b', r'\\bora\\b', r'\\biku\\b']\n", "            },\n", "            'sundanese': {\n", "                'words': ['jeung', 'sareng', 'ieu', 'eta', 'ti', 'pikeun', 'di', 'henteu', 'teu', 'geus', 'tiasa', 'oge'],\n", "                'patterns': [r'\\bjeung\\b', r'\\bsareng\\b', r'\\bhenteu\\b', r'\\bteu\\b']\n", "            }\n", "        }\n", "    \n", "    def detect_language_mix(self, text: str) -> Dict[str, float]:\n", "        \"\"\"Detect language mix in text.\"\"\"\n", "        text_lower = text.lower()\n", "        scores = {'indonesian': 0, 'javanese': 0, 'sundanese': 0}\n", "        \n", "        for lang, patterns in self.language_patterns.items():\n", "            word_matches = sum(1 for word in patterns['words'] if word in text_lower)\n", "            pattern_matches = sum(1 for pattern in patterns['patterns'] if re.search(pattern, text_lower))\n", "            \n", "            total_words = len(text_lower.split())\n", "            if total_words > 0:\n", "                scores[lang] = (word_matches + pattern_matches) / total_words\n", "        \n", "        return scores\n", "    \n", "    def get_dominant_language(self, text: str) -> str:\n", "        \"\"\"Get dominant language.\"\"\"\n", "        scores = self.detect_language_mix(text)\n", "        return max(scores, key=scores.get)\n", "\n", "class NusaXTextPreprocessor:\n", "    \"\"\"Text preprocessor for NusaX languages.\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.abbreviations = {\n", "            'gk': 'tidak', 'ga': 'tidak', 'gak': 'tidak', 'udh': 'sudah', 'udah': 'sudah',\n", "            'blm': 'belum', 'blom': 'belum', 'krn': 'karena', 'krna': 'karena',\n", "            'dgn': 'dengan', 'sm': 'sama', 'tp': 'tapi', 'trs': 'terus',\n", "            'yg': 'yang', 'utk': 'untuk', 'dr': 'dari', 'org': 'orang',\n", "            'bgt': 'banget', 'bener': 'benar', 'emg': 'memang', 'emang': 'memang'\n", "        }\n", "    \n", "    def expand_abbreviations(self, text: str) -> str:\n", "        \"\"\"Expand abbreviations.\"\"\"\n", "        words = text.split()\n", "        expanded_words = []\n", "        \n", "        for word in words:\n", "            word_lower = word.lower()\n", "            if word_lower in self.abbreviations:\n", "                expanded_words.append(self.abbreviations[word_lower])\n", "            else:\n", "                expanded_words.append(word)\n", "        \n", "        return ' '.join(expanded_words)\n", "    \n", "    def clean_text(self, text: str) -> str:\n", "        \"\"\"Clean and preprocess text.\"\"\"\n", "        text = text.lower()\n", "        text = self.expand_abbreviations(text)\n", "        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\\\(\\\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)\n", "        text = re.sub(r'@\\w+', '', text)\n", "        text = re.sub(r'#\\w+', '', text)\n", "        text = re.sub(r'\\s+', ' ', text)\n", "        text = re.sub(r'[^\\w\\s.,!?-]', '', text)\n", "        return text.strip()\n", "\n", "# Initialize processors\n", "language_detector = NusaXLanguageDetector()\n", "text_preprocessor = NusaXTextPreprocessor()\n", "\n", "print(\"✅ NusaX language processing tools initialized!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Test Language Detection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test language detection with sample texts\n", "test_texts = [\n", "    \"Aplikasi ini sangat bagus dan mudah digunakan\",\n", "    \"Aplikasi iki apik tenan lan gampang digunakake\", \n", "    \"Aplikasi ieu saé pisan sareng gampang dianggo\",\n", "    \"Instagram bagus bgt tapi kadang lemot juga sih\",\n", "    \"Je<PERSON><PERSON> banget aplikasinya, sering error lan ora iso dibuka\"\n", "]\n", "\n", "print(\"🧪 Testing Language Detection:\")\n", "print(\"=\" * 60)\n", "\n", "for i, text in enumerate(test_texts, 1):\n", "    scores = language_detector.detect_language_mix(text)\n", "    dominant = language_detector.get_dominant_language(text)\n", "    cleaned = text_preprocessor.clean_text(text)\n", "    \n", "    print(f\"\\n{i}. Original: {text}\")\n", "    print(f\"   Cleaned:  {cleaned}\")\n", "    print(f\"   Language: {dominant}\")\n", "    print(f\"   Scores:   {scores}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Load Sentiment Analysis Models"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class NusaXSentimentAnalyzer:\n", "    \"\"\"Sentiment analyzer using NusaX-compatible models.\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.pipelines = {}\n", "        self.language_codes = {\n", "            'indonesian': 'id',\n", "            'javanese': 'jv', \n", "            'sundanese': 'su'\n", "        }\n", "        \n", "    def load_models(self):\n", "        \"\"\"Load sentiment analysis models.\"\"\"\n", "        print(\"🤖 Loading sentiment analysis models...\")\n", "        \n", "        try:\n", "            # Primary model - multilingual sentiment\n", "            model_name = \"cardiffnlp/twitter-xlm-roberta-base-sentiment\"\n", "            print(f\"   Loading {model_name}...\")\n", "            \n", "            sentiment_pipeline = pipeline(\n", "                \"sentiment-analysis\",\n", "                model=model_name,\n", "                device=0 if torch.cuda.is_available() else -1\n", "            )\n", "            \n", "            # Use same model for all languages (in practice, you'd have separate models)\n", "            for lang in self.language_codes.keys():\n", "                self.pipelines[lang] = sentiment_pipeline\n", "                \n", "            print(\"✅ Primary model loaded successfully!\")\n", "            \n", "        except Exception as e:\n", "            print(f\"⚠️  Primary model failed: {e}\")\n", "            print(\"   Loading fallback model...\")\n", "            self.load_fallback_model()\n", "    \n", "    def load_fallback_model(self):\n", "        \"\"\"Load fallback sentiment model.\"\"\"\n", "        try:\n", "            sentiment_pipeline = pipeline(\n", "                \"sentiment-analysis\", \n", "                model=\"nlptown/bert-base-multilingual-uncased-sentiment\"\n", "            )\n", "            for lang in self.language_codes.keys():\n", "                self.pipelines[lang] = sentiment_pipeline\n", "            print(\"✅ Fallback model loaded successfully!\")\n", "        except Exception as e:\n", "            print(f\"❌ Error loading fallback model: {e}\")\n", "    \n", "    def analyze_sentiment(self, text: str, language: str = None) -> Dict:\n", "        \"\"\"Analyze sentiment of text.\"\"\"\n", "        if not text or not text.strip():\n", "            return {\n", "                'sentiment': 'neutral',\n", "                'confidence': 0.0,\n", "                'language': 'unknown'\n", "            }\n", "        \n", "        # Detect language if not provided\n", "        if language is None:\n", "            language = language_detector.get_dominant_language(text)\n", "        \n", "        # Preprocess text\n", "        processed_text = text_preprocessor.clean_text(text)\n", "        if len(processed_text) > 512:\n", "            processed_text = processed_text[:512]\n", "        \n", "        try:\n", "            # Get appropriate pipeline\n", "            pipeline = self.pipelines.get(language, self.pipelines.get('indonesian'))\n", "            \n", "            if pipeline is None:\n", "                return {\n", "                    'sentiment': 'neutral',\n", "                    'confidence': 0.0,\n", "                    'language': language\n", "                }\n", "            \n", "            # Perform sentiment analysis\n", "            result = pipeline(processed_text)\n", "            \n", "            if isinstance(result, list) and len(result) > 0:\n", "                result = result[0]\n", "            \n", "            # Normalize labels\n", "            label = result['label'].lower()\n", "            if 'pos' in label or label == 'positive':\n", "                sentiment = 'positive'\n", "            elif 'neg' in label or label == 'negative':\n", "                sentiment = 'negative'\n", "            else:\n", "                sentiment = 'neutral'\n", "            \n", "            return {\n", "                'sentiment': sentiment,\n", "                'confidence': result['score'],\n", "                'language': language\n", "            }\n", "            \n", "        except Exception as e:\n", "            print(f\"Error analyzing sentiment: {e}\")\n", "            return {\n", "                'sentiment': 'neutral',\n", "                'confidence': 0.0,\n", "                'language': language\n", "            }\n", "\n", "# Initialize sentiment analyzer\n", "sentiment_analyzer = NusaXSentimentAnalyzer()\n", "sentiment_analyzer.load_models()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Test Sentiment Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test sentiment analysis\n", "test_reviews = [\n", "    \"Instagram sangat bagus! Fiturnya lengkap dan mudah digunakan\",\n", "    \"Aplikasi jelek banget, sering error dan lemot\",\n", "    \"<PERSON><PERSON>a aja sih, tidak ada yang istimewa\",\n", "    \"Love this app! Tapi kadang suka ngelag\",\n", "    \"Aplik<PERSON> iki apik tenan, nanging kadhang angel dibukak\"\n", "]\n", "\n", "print(\"🧪 Testing Sentiment Analysis:\")\n", "print(\"=\" * 60)\n", "\n", "for i, review in enumerate(test_reviews, 1):\n", "    result = sentiment_analyzer.analyze_sentiment(review)\n", "    \n", "    print(f\"\\n{i}. Review: {review}\")\n", "    print(f\"   Sentiment: {result['sentiment']} (confidence: {result['confidence']:.3f})\")\n", "    print(f\"   Language: {result['language']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Scrape Instagram Reviews"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def scrape_instagram_reviews(count: int = 2000) -> List[Dict]:\n", "    \"\"\"Scrape Instagram reviews from Google Play Store.\"\"\"\n", "    \n", "    print(f\"📱 Scraping {count} Instagram reviews...\")\n", "    \n", "    try:\n", "        # Get app information\n", "        app_info = app(\n", "            CONFIG['app_id'], \n", "            lang=CONFIG['lang'], \n", "            country=CONFIG['country']\n", "        )\n", "        \n", "        print(f\"📊 App: {app_info['title']}\")\n", "        print(f\"👨‍💻 Developer: {app_info['developer']}\")\n", "        print(f\"⭐ Rating: {app_info['score']:.2f}\")\n", "        print(f\"📝 Total Reviews: {app_info['reviews']:,}\")\n", "        \n", "        # Scrape reviews in batches\n", "        all_reviews = []\n", "        continuation_token = None\n", "        batch_size = CONFIG['batch_size']\n", "        \n", "        with tqdm(total=count, desc=\"Scraping reviews\") as pbar:\n", "            while len(all_reviews) < count:\n", "                try:\n", "                    result, continuation_token = reviews(\n", "                        CONFIG['app_id'],\n", "                        lang=CONFIG['lang'],\n", "                        country=CONFIG['country'],\n", "                        sort=CONFIG['sort_order'],\n", "                        count=min(batch_size, count - len(all_reviews)),\n", "                        continuation_token=continuation_token\n", "                    )\n", "                    \n", "                    if not result:\n", "                        print(\"\\n⚠️  No more reviews available\")\n", "                        break\n", "                        \n", "                    all_reviews.extend(result)\n", "                    pbar.update(len(result))\n", "                    \n", "                    # Add delay to avoid rate limiting\n", "                    time.sleep(CONFIG['delay_between_batches'])\n", "                    \n", "                except Exception as e:\n", "                    print(f\"\\n❌ Error scraping batch: {e}\")\n", "                    break\n", "        \n", "        print(f\"\\n✅ Successfully scraped {len(all_reviews)} reviews\")\n", "        return all_reviews\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error scraping reviews: {e}\")\n", "        return []\n", "\n", "# Scrape reviews\n", "print(\"🚀 Starting review scraping...\")\n", "reviews_data = scrape_instagram_reviews(CONFIG['review_count'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Process Reviews with Sentiment Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Convert to DataFrame and process\n", "if reviews_data:\n", "    print(\"📊 Converting to DataFrame...\")\n", "    df = pd.DataFrame(reviews_data)\n", "    \n", "    print(f\"✅ Created DataFrame with {len(df)} reviews\")\n", "    print(f\"📅 Date range: {df['at'].min()} to {df['at'].max()}\")\n", "    \n", "    # Display sample reviews\n", "    print(\"\\n📝 Sample reviews:\")\n", "    for i, review in enumerate(df['content'].head(3), 1):\n", "        print(f\"{i}. {review[:100]}...\")\n", "else:\n", "    print(\"❌ No reviews data available\")\n", "    df = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Perform sentiment analysis on all reviews\n", "if not df.empty:\n", "    print(\"🤖 Performing sentiment analysis on all reviews...\")\n", "    \n", "    sentiments = []\n", "    confidences = []\n", "    languages = []\n", "    \n", "    for review in tqdm(df['content'], desc=\"Analyzing sentiment\"):\n", "        result = sentiment_analyzer.analyze_sentiment(review)\n", "        sentiments.append(result['sentiment'])\n", "        confidences.append(result['confidence'])\n", "        languages.append(result['language'])\n", "    \n", "    # Add results to DataFrame\n", "    df['sentiment'] = sentiments\n", "    df['sentiment_confidence'] = confidences\n", "    df['detected_language'] = languages\n", "    \n", "    # Add additional features\n", "    df['review_length'] = df['content'].str.len()\n", "    df['word_count'] = df['content'].str.split().str.len()\n", "    \n", "    print(\"✅ Sentiment analysis completed!\")\n", "    print(f\"📊 Processed {len(df)} reviews\")\n", "else:\n", "    print(\"❌ No data to analyze\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Data Analysis and Insights"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate comprehensive report\n", "if not df.empty:\n", "    print(\"📈 SENTIMENT ANALYSIS REPORT\")\n", "    print(\"=\" * 60)\n", "    \n", "    print(f\"\\n📊 Total Reviews Analyzed: {len(df):,}\")\n", "    print(f\"📅 Date Range: {df['at'].min()} to {df['at'].max()}\")\n", "    \n", "    # Sentiment distribution\n", "    print(\"\\n😊 Sentiment Distribution:\")\n", "    sentiment_counts = df['sentiment'].value_counts()\n", "    for sentiment, count in sentiment_counts.items():\n", "        percentage = (count / len(df)) * 100\n", "        emoji = \"😊\" if sentiment == \"positive\" else \"😞\" if sentiment == \"negative\" else \"😐\"\n", "        print(f\"   {emoji} {sentiment.capitalize()}: {count:,} ({percentage:.1f}%)\")\n", "    \n", "    # Language distribution\n", "    print(\"\\n🌍 Language Distribution:\")\n", "    lang_counts = df['detected_language'].value_counts()\n", "    for lang, count in lang_counts.items():\n", "        percentage = (count / len(df)) * 100\n", "        print(f\"   🗣️  {lang.capitalize()}: {count:,} ({percentage:.1f}%)\")\n", "    \n", "    # Rating vs Sentiment correlation\n", "    print(\"\\n⭐ Rating vs Sentiment:\")\n", "    rating_sentiment = df.groupby(['score', 'sentiment']).size().unstack(fill_value=0)\n", "    print(rating_sentiment)\n", "    \n", "    # Average confidence by sentiment\n", "    print(\"\\n🎯 Average Confidence by Sentiment:\")\n", "    conf_by_sentiment = df.groupby('sentiment')['sentiment_confidence'].mean()\n", "    for sentiment, conf in conf_by_sentiment.items():\n", "        print(f\"   {sentiment.capitalize()}: {conf:.3f}\")\n", "    \n", "    # Review length statistics\n", "    print(\"\\n📝 Review Length Statistics:\")\n", "    print(f\"   Average length: {df['review_length'].mean():.1f} characters\")\n", "    print(f\"   Average words: {df['word_count'].mean():.1f} words\")\n", "    print(f\"   Longest review: {df['review_length'].max()} characters\")\n", "    print(f\"   Shortest review: {df['review_length'].min()} characters\")\n", "else:\n", "    print(\"❌ No data available for analysis\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Visualizations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comprehensive visualizations\n", "if not df.empty:\n", "    fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "    fig.suptitle('Instagram App Reviews - Sentiment Analysis Report', fontsize=16, fontweight='bold')\n", "    \n", "    # 1. Sentiment Distribution (Pie Chart)\n", "    sentiment_counts = df['sentiment'].value_counts()\n", "    colors = ['#2ecc71', '#e74c3c', '#95a5a6']  # Green, <PERSON>, <PERSON>\n", "    axes[0, 0].pie(sentiment_counts.values, labels=sentiment_counts.index, autopct='%1.1f%%', \n", "                   colors=colors, startangle=90)\n", "    axes[0, 0].set_title('Sentiment Distribution')\n", "    \n", "    # 2. Rating vs Sentiment (Stacked Bar)\n", "    rating_sentiment = df.groupby(['score', 'sentiment']).size().unstack(fill_value=0)\n", "    rating_sentiment.plot(kind='bar', ax=axes[0, 1], stacked=True, color=colors)\n", "    axes[0, 1].set_title('Rating vs Sentiment')\n", "    axes[0, 1].set_xlabel('Rating (Stars)')\n", "    axes[0, 1].set_ylabel('Number of Reviews')\n", "    axes[0, 1].legend(title='Sentiment')\n", "    axes[0, 1].tick_params(axis='x', rotation=0)\n", "    \n", "    # 3. Language Distribution (Bar Chart)\n", "    lang_counts = df['detected_language'].value_counts()\n", "    axes[0, 2].bar(lang_counts.index, lang_counts.values, color='#3498db')\n", "    axes[0, 2].set_title('Language Distribution')\n", "    axes[0, 2].set_xlabel('Language')\n", "    axes[0, 2].set_ylabel('Number of Reviews')\n", "    axes[0, 2].tick_params(axis='x', rotation=45)\n", "    \n", "    # 4. Sentiment Confidence Distribution (Histogram)\n", "    axes[1, 0].hist(df['sentiment_confidence'], bins=20, alpha=0.7, color='#9b59b6')\n", "    axes[1, 0].set_title('Sentiment Confidence Distribution')\n", "    axes[1, 0].set_xlabel('Confidence Score')\n", "    axes[1, 0].set_ylabel('Frequency')\n", "    \n", "    # 5. Review Length vs Sentiment (Box Plot)\n", "    sentiment_order = ['negative', 'neutral', 'positive']\n", "    df_plot = df[df['sentiment'].isin(sentiment_order)]\n", "    sns.boxplot(data=df_plot, x='sentiment', y='review_length', ax=axes[1, 1], order=sentiment_order)\n", "    axes[1, 1].set_title('Review Length by Sentiment')\n", "    axes[1, 1].set_xlabel('Sentiment')\n", "    axes[1, 1].set_ylabel('Review Length (characters)')\n", "    \n", "    # 6. Sentiment by Language (Stacked Bar)\n", "    lang_sentiment = df.groupby(['detected_language', 'sentiment']).size().unstack(fill_value=0)\n", "    lang_sentiment.plot(kind='bar', ax=axes[1, 2], stacked=True, color=colors)\n", "    axes[1, 2].set_title('Sentiment by Language')\n", "    axes[1, 2].set_xlabel('Language')\n", "    axes[1, 2].set_ylabel('Number of Reviews')\n", "    axes[1, 2].legend(title='Sentiment')\n", "    axes[1, 2].tick_params(axis='x', rotation=45)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"📊 Visualizations generated successfully!\")\n", "else:\n", "    print(\"❌ No data available for visualization\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 11. Export Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save results to CSV\n", "if not df.empty:\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    filename = f\"instagram_reviews_sentiment_{timestamp}.csv\"\n", "    \n", "    # Save to CSV\n", "    df.to_csv(filename, index=False, encoding='utf-8')\n", "    print(f\"💾 Results saved to: {filename}\")\n", "    print(f\"📊 File size: {os.path.getsize(filename) / 1024 / 1024:.2f} MB\")\n", "    \n", "    # Display column information\n", "    print(f\"\\n📋 Dataset Info:\")\n", "    print(f\"   Rows: {len(df):,}\")\n", "    print(f\"   Columns: {len(df.columns)}\")\n", "    print(f\"   Columns: {list(df.columns)}\")\n", "    \n", "    # Show sample of final dataset\n", "    print(f\"\\n📝 Sample of processed data:\")\n", "    display_cols = ['content', 'score', 'sentiment', 'sentiment_confidence', 'detected_language']\n", "    print(df[display_cols].head())\n", "else:\n", "    print(\"❌ No data to save\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 12. Sample Analysis - Mixed Language Reviews"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Find and analyze interesting mixed-language reviews\n", "if not df.empty:\n", "    print(\"🔍 Analyzing Mixed-Language Reviews:\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Find reviews with mixed languages (containing both Indonesian and regional language words)\n", "    mixed_reviews = []\n", "    \n", "    for idx, row in df.iterrows():\n", "        text = row['content'].lower()\n", "        scores = language_detector.detect_language_mix(text)\n", "        \n", "        # Check if multiple languages have significant scores\n", "        significant_langs = [lang for lang, score in scores.items() if score > 0.1]\n", "        \n", "        if len(significant_langs) > 1:\n", "            mixed_reviews.append({\n", "                'content': row['content'],\n", "                'sentiment': row['sentiment'],\n", "                'confidence': row['sentiment_confidence'],\n", "                'detected_language': row['detected_language'],\n", "                'language_scores': scores\n", "            })\n", "    \n", "    print(f\"🌍 Found {len(mixed_reviews)} mixed-language reviews\")\n", "    \n", "    # Show some examples\n", "    if mixed_reviews:\n", "        print(\"\\n📝 Examples of Mixed-Language Reviews:\")\n", "        for i, review in enumerate(mixed_reviews[:5], 1):\n", "            print(f\"\\n{i}. {review['content'][:100]}...\")\n", "            print(f\"   Sentiment: {review['sentiment']} (confidence: {review['confidence']:.3f})\")\n", "            print(f\"   Language scores: {review['language_scores']}\")\n", "    \n", "    # Analyze sentiment by language\n", "    print(\"\\n📊 Sentiment Analysis by Language:\")\n", "    for lang in df['detected_language'].unique():\n", "        lang_df = df[df['detected_language'] == lang]\n", "        sentiment_dist = lang_df['sentiment'].value_counts(normalize=True) * 100\n", "        avg_confidence = lang_df['sentiment_confidence'].mean()\n", "        \n", "        print(f\"\\n🗣️  {lang.capitalize()} ({len(lang_df)} reviews):\")\n", "        for sentiment, percentage in sentiment_dist.items():\n", "            print(f\"   {sentiment}: {percentage:.1f}%\")\n", "        print(f\"   Average confidence: {avg_confidence:.3f}\")\n", "else:\n", "    print(\"❌ No data available for mixed-language analysis\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 13. Summary and Conclusions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final summary\n", "if not df.empty:\n", "    print(\"🎉 ANALYSIS COMPLETE!\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Key insights\n", "    total_reviews = len(df)\n", "    positive_pct = (df['sentiment'] == 'positive').sum() / total_reviews * 100\n", "    negative_pct = (df['sentiment'] == 'negative').sum() / total_reviews * 100\n", "    neutral_pct = (df['sentiment'] == 'neutral').sum() / total_reviews * 100\n", "    \n", "    avg_rating = df['score'].mean()\n", "    avg_confidence = df['sentiment_confidence'].mean()\n", "    \n", "    indonesian_pct = (df['detected_language'] == 'indonesian').sum() / total_reviews * 100\n", "    \n", "    print(f\"📊 KEY INSIGHTS:\")\n", "    print(f\"   • Analyzed {total_reviews:,} Instagram reviews from Indonesian Play Store\")\n", "    print(f\"   • Overall sentiment: {positive_pct:.1f}% positive, {negative_pct:.1f}% negative, {neutral_pct:.1f}% neutral\")\n", "    print(f\"   • Average rating: {avg_rating:.2f}/5 stars\")\n", "    print(f\"   • Average sentiment confidence: {avg_confidence:.3f}\")\n", "    print(f\"   • Language distribution: {indonesian_pct:.1f}% Indonesian, {100-indonesian_pct:.1f}% regional languages\")\n", "    \n", "    print(f\"\\n💾 OUTPUT FILES:\")\n", "    print(f\"   • CSV file with all results: instagram_reviews_sentiment_*.csv\")\n", "    print(f\"   • Interactive visualizations displayed above\")\n", "    \n", "    print(f\"\\n🔬 TECHNICAL DETAILS:\")\n", "    print(f\"   • Used NusaX-compatible models for multi-language sentiment analysis\")\n", "    print(f\"   • Processed Indonesian, Javanese, and Sundanese text\")\n", "    print(f\"   • Applied text preprocessing and abbreviation expansion\")\n", "    print(f\"   • Scraped from Google Play Store with rate limiting\")\n", "    \n", "    print(f\"\\n✨ This analysis demonstrates the capability to process mixed-code Indonesian reviews\")\n", "    print(f\"   using NusaX methodology for regional language sentiment analysis.\")\n", "else:\n", "    print(\"❌ Analysis could not be completed due to lack of data\")\n", "    print(\"   Please check your internet connection and try again.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}